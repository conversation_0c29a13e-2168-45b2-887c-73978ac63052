openapi: 3.0.3
info:
  version: 1.7.0
  title: Limits Velocity API
  description: >-
    An API that allows a partner to manage limits velocities
  termsOfService: http://www.bnc.ca/
  contact:
    name: Support PAYPRO
    email: <EMAIL>
    url: https://www.bnc.ca
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html
  x-api-id: dd4ad57c-616d-4ef7-93c9-92adc75316e1
  x-app-id: 6641
  x-audience:
    - internal
servers:
  - url: https://pmt.apis.bnc.ca/
    description: Production Environment
    x-stage-id: prod
    x-environment: production
  - url: https://pmt-tu.apis.bngf.local/
    description: TU Environment
    x-stage-id: tu
    x-environment: non_production
  - url: https://pmt-ti.apis.bngf.local/
    description: TI Environment
    x-stage-id: ti
    x-environment: non_production
  - url: https://pmt-ta.apis.bngf.local/
    description: TA Environment
    x-stage-id: ta
    x-environment: non_production
  - url: https://pmt-pp.apis.bngf.local/
    description: PP Environment
    x-stage-id: pp
    x-environment: non_production

paths:
  /velocities:
    parameters:
      - $ref: "#/components/parameters/LimitType"
      - $ref: "#/components/parameters/ClientId"
      - $ref: "#/components/parameters/ChannelId"
      - $ref: "#/components/parameters/Version"
      - $ref: "#/components/parameters/RequestId"
    get:
      tags:
        - Limits Velocities
      summary: Retrieve limits.
      description: >-
        This service allows a partner to retrieve limits velocities available for a specific client.
      security:
        - oAuthScope:
            - system:limits:velocity:search
      operationId: getVelocities
      responses:
        '400': {"$ref": "#/components/responses/400-bad-request"}
        '404': {"$ref": "#/components/responses/404-not-found"}
        '500': {"$ref": "#/components/responses/500-internal-server-error"}
        '200':
          description: Limits Velocities
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/VelocitiesList"

  /velocities/increment:
    parameters:
      - $ref: "#/components/parameters/ChannelId"
      - $ref: "#/components/parameters/Version"
      - $ref: "#/components/parameters/RequestId"
    post:
      tags:
        - Limits Velocities
      summary: Increment limits.
      description: >-
        This service allows a partner to increment limits velocities for a specific client.
      security:
        - oAuthScope:
            - system:limits:velocity:update
      operationId: incrementVelocities
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VelocitiesIncrement'
      responses:
        '204': {"$ref": "#/components/responses/204-no-content"}
        '400': {"$ref": "#/components/responses/400-bad-request"}
        '500': {"$ref": "#/components/responses/500-internal-server-error"}

  /velocities/decrement:
    parameters:
      - $ref: "#/components/parameters/ChannelId"
      - $ref: "#/components/parameters/Version"
      - $ref: "#/components/parameters/RequestId"
    post:
      tags:
        - Limits Velocities
      summary: Decrement limits.
      description: >-
        This service allows a partner to decrement limits velocities for a specific client.
      security:
        - oAuthScope:
            - system:limits:velocity:update
      operationId: decrementVelocities
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VelocitiesDecrement'
      responses:
        '204': {"$ref": "#/components/responses/204-no-content"}
        '400': {"$ref": "#/components/responses/400-bad-request"}
        '500': {"$ref": "#/components/responses/500-internal-server-error"}

  /velocities/adjust:
    parameters:
      - $ref: "#/components/parameters/ChannelId"
      - $ref: "#/components/parameters/Version"
      - $ref: "#/components/parameters/RequestId"
    post:
      tags:
        - Limits Velocities
      summary: Adjust limits.
      description: >-
        This service allows a partner to adjust limits velocities for a specific client.
      security:
        - oAuthScope:
            - system:limits:velocity:update
      operationId: adjustVelocities
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VelocitiesAdjust'
      responses:
        '204': {"$ref": "#/components/responses/204-no-content"}
        '400': {"$ref": "#/components/responses/400-bad-request"}
        '500': {"$ref": "#/components/responses/500-internal-server-error"}

  /velocities/simulation:
    parameters:
      - $ref: "#/components/parameters/ChannelId"
      - $ref: "#/components/parameters/Version"
      - $ref: "#/components/parameters/RequestId"
    post:
      tags:
        - Simulation
      summary: Simulate limits.
      description: >-
        This service allows a partner to simulate limit velocitie for a specific client.
      security:
        - oAuthScope:
            - system:limits:velocity:update
      operationId: simulationVelocities
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VelocitiesSimulation'
      responses:
        '204': {"$ref": "#/components/responses/204-no-content"}
        '400': {"$ref": "#/components/responses/400-bad-request"}
        '500': {"$ref": "#/components/responses/500-internal-server-error"}

components:

  parameters:
    LimitType:
      in: query
      name: limitType
      required: false
      description: >-
        Indicates the limit type.
      schema:
        $ref: '#/components/schemas/LimitType'

    ClientId:
      in: header
      name: x-client-id
      required: true
      description: >-
        The BNC id of the client.
      schema:
        $ref: '#/components/schemas/ClientId'

    ChannelId:
      in: header
      name: x-channel-id
      required: true
      description: >-
        The application source code.
      schema:
        $ref: '#/components/schemas/ChannelId'

    Version:
      in: header
      name: Accept-version
      description: >-
        The Major version of the API.
      schema:
        type: string
      required: true
      example: v1

    RequestId:
      in: header
      name: x-request-id
      required: true
      description: >-
        Unique ID generated for each request used for message tracking purposes.
        Technical and unique traceability identifier. Used by monitoring and log tolls such as Datadog and Splunk.
      schema:
        $ref: '#/components/schemas/RequestId'

  # SECURITY
  securitySchemes:
    oAuthScope:
      type: oauth2
      description: This API uses OAuth 2 with the implicit grant flow.
      flows:
        clientCredentials:
          tokenUrl: https://api-ti.bnc.ca/bnc/ti-out/sso/oauth2/ausb2snfqxccuEfI90h7/v1/token
          scopes:
            system:limits:velocity:search : search resource
            system:limits:velocity:update : update resource

  responses:
    204-no-content:
      description: NO CONTENT - If the request has succeeded and has led to action on the resource.

    400-bad-request:
      description: BAD REQUEST - If the request is not valid.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Errors"

    404-not-found:
      description: RESOURCE NOT FOUND

    500-internal-server-error:
      description: INTERNAL SERVER ERROR - If the server encountered an unexpected condition.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Errors"

  schemas:

    ChannelId:
      description: >-
        The application source code.
      type: string
      example: 5156
      minLength: 1
      maxLength: 4

    ClientId:
      description: >-
        The BNC id of the client.
      type: string
      example: 2C811E20AEE80A2860764937595FFE76DBF72788941F0C7726CC626949350900
      minLength: 1
      maxLength: 128

    RequestId:
      description: >-
        Unique ID generated for each request used for message tracking purposes. Technical and unique traceability identifier. Used by monitoring and log tolls such as Datadog and Splunk.
      type: string
      example: 3bcdf7fa-7e48-4565-9751-d8acbdb64d8b
      minLength: 1
      maxLength: 36

    Daily:
      description: Daily total amount (24 hours)
      type: number
      example: 3000.00
      x-precision: 2

    Weekly:
      description: Weekly total amount (7 days)
      type: number
      example: 80000.00
      x-precision: 2

    Monthly:
      description: Monthly total amount (30 days)
      type: number
      example: 125000.00
      x-precision: 2

    LimitType:
      description: Indicates the limit type
      type: string
      enum: ['DOMESTIC', 'DOMESTIC_INTERAC_ANR', 'DOMESTIC_INTERAC_MONEYREQUEST', 'DOMESTIC_INTERAC_RTANR', 'DOMESTIC_INTERAC_REGULAR', 'DOMESTIC_ON_US', 'KONEK_PAYMENT', 'KONEK_PAYMENT_ABP', 'BILL_PAYMENT', 'OTHER_BILLER']
      example: DOMESTIC_INTERAC_REGULAR

    ParentLimitType:
      description: Indicates the limit type parent
      type: string
      enum: ['DOMESTIC', 'KONEK_PAYMENT', 'BILL_PAYMENT', 'OTHER_BILLER']
      example: DOMESTIC

    ClientType:
      description: Indicates if Client is Individual or Organisation
      type: string
      minLength: 1
      maxLength: 20
      enum: ['INDIVIDUAL', 'ORGANISATION']
      example: INDIVIDUAL

    SimulationType:
      description: >-
        Indicates the simulation type
        
        - BANK-LIMITS: Simulate increment with BANK-LIMITS only
        
        - VELOCITIES: Simulate increment with all limits (BANK-LIMITS, CUSTOM-LIMITS) and RUNNING-SUM
      type: string
      enum: ['BANK-LIMITS', 'VELOCITIES']
      example: VELOCITIES

    OffHost:
      description: Indicates if context is Off Host
      type: boolean
      example: true

    PostingDateTime:
      description: ISO 8601 Payment Transaction Timestamp in UTC (YYYY-MM-DDThh:mm:ss.sssZ)
      type: string
      format: date-time
      example: "2019-05-05T17:29:12.123Z"

    EndToEndIdentification:
      description: "EndToEndIdentification is a business transaction identifier used across BNC systems (end-to-end chain). The same EndToEndIdentification must be used accross all of a velocity's operations (increment/decrement/adjust)."
      type: string
      minLength: 1
      maxLength: 32
      example: 3bcdf7fa7e4845659751d8acbdb64d8b

    InstructionIdentification:
      description: The instruction Id identifies the current request and is tied to the EndToEndIdentification
      type: string
      title: InstructionIdentification
      minLength: 1
      maxLength: 35
      example: 028be1ecb2244273981ef55f059ab25a

    OriginalInstructionIdentification:
      description: The original instruction id.
      type: string
      title: OriginalInstructionIdentification
      minLength: 1
      maxLength: 35
      example: c304ece65b4045a9a4f12cfc32a2a505

    Amount:
      description: Amount of the transaction
      type: number
      example: 44.44
      x-precision: 2

    VelocitiesSimulation:
      type: object
      properties:
        simulationType:
          $ref: '#/components/schemas/SimulationType'
        limitType:
          $ref: '#/components/schemas/LimitType'
        endToEndIdentification:
          $ref: '#/components/schemas/EndToEndIdentification'
        instructionIdentification:
          $ref: '#/components/schemas/InstructionIdentification'
        clientId:
          $ref: '#/components/schemas/ClientId'
        clientType:
          $ref: '#/components/schemas/ClientType'
        amount:
          $ref: '#/components/schemas/Amount'
        postingDate:
          $ref: '#/components/schemas/PostingDateTime'
      required:
        - simulationType
        - limitType
        - endToEndIdentification
        - instructionIdentification
        - clientId
        - clientType
        - amount
        - postingDate

    VelocitiesIncrement:
      type: object
      properties:
        limitType:
          $ref: '#/components/schemas/LimitType'
        endToEndIdentification:
          $ref: '#/components/schemas/EndToEndIdentification'
        instructionIdentification:
          $ref: '#/components/schemas/InstructionIdentification'
        clientId:
          $ref: '#/components/schemas/ClientId'
        clientType:
          $ref: '#/components/schemas/ClientType'
        amount:
          $ref: '#/components/schemas/Amount'
        postingDate:
          $ref: '#/components/schemas/PostingDateTime'
        offHost:
          $ref: '#/components/schemas/OffHost'
      required:
        - limitType
        - endToEndIdentification
        - instructionIdentification
        - clientId
        - clientType
        - amount
        - postingDate

    VelocitiesDecrement:
      type: object
      properties:
        endToEndIdentification:
          $ref: '#/components/schemas/EndToEndIdentification'
        instructionIdentification:
          $ref: '#/components/schemas/InstructionIdentification'
        originalInstructionIdentification:
          $ref: '#/components/schemas/OriginalInstructionIdentification'
      required:
        - endToEndIdentification
        - instructionIdentification
        - originalInstructionIdentification

    VelocitiesAdjust:
      type: object
      properties:
        endToEndIdentification:
          $ref: '#/components/schemas/EndToEndIdentification'
        instructionIdentification:
          $ref: '#/components/schemas/InstructionIdentification'
        originalInstructionIdentification:
          $ref: '#/components/schemas/OriginalInstructionIdentification'
        amount:
          $ref: '#/components/schemas/Amount'
      required:
        - endToEndIdentification
        - instructionIdentification
        - originalInstructionIdentification
        - amount

    VelocitiesList:
      type: object
      description: Returns all velocities for a specific client
      properties:
        velocities:
          type: array
          items:
            $ref: '#/components/schemas/VelocitiesByType'

    VelocitiesByType:
      type: object
      properties:
        limitType:
          $ref: '#/components/schemas/LimitType'
        parentLimitType:
          $ref: '#/components/schemas/ParentLimitType'
        daily:
          $ref: '#/components/schemas/Daily'
        weekly:
          $ref: '#/components/schemas/Weekly'
        monthly:
          $ref: '#/components/schemas/Monthly'
      required:
        - limitType
        - parentLimitType


    Errors:
      type: object
      description: Returns a list of errors.
      properties:
        errors:
          type: array
          items:
            $ref: '#/components/schemas/Error'

    Error:
      description: Error code.
      type: object
      title: Error
      required:
        - code
        - text
        - origin
      properties:
        code:
          description: Error Source Code.
          title: Code
          type: string
          maxLength: 50
          example: "SYSTEM_ERROR"
        text:
          description: Description.
          title: Text
          type: string
          maxLength: 2000
          example: "Error description"
        origin:
          description: Error source.
          title: Origin
          type: string
          maxLength: 50
          example: "pmt-limits-velocity-api"
        rule:
          description: Business rule.
          title: Rule
          type: string
          maxLength: 50
          example: "RULE_XXX"