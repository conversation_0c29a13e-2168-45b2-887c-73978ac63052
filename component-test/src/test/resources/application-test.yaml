app:
  directParticipantIdentifier: CA000006
  name: pmt-etransfer-payment-processing-simulation-api
applicationmanagement:
  thresholdheapinpercent: 95
logging:
  level:
    ca.bnc.payment: INFO
    ca.nbc.payment: INFO
    org.springframework: INFO
    org.springframework.web: INFO
management:
  endpoint:
    health:
      group:
        liveness:
          include: heapIndicator, ping
          show-details: always
        readiness:
          include: ping
          show-details: always
      show-details: always
  health:
    defaults:
      enabled: true
    jms:
      enabled: true
    mapping: null
    show-details: always
    web:
      exposure:
        include: '*'
  server:
    port: 9999
okta:
  enabled: true
  jwks:
    authorizationServerId: ausmzf2ge2Rp3L3nS1d6
    url: "fake_jwks_url"
    clientId: "fake_client_id"
    privateKey: "fake_private_key"
  tokenDetails:
    limitsVelocityScope:
      scope: "system:limits:velocity:search"
    paymentRegistrationScope:
      scope: "payments:registration:update"
providers:
  bank-account-connector:
    retry:
      attempts: 3
      backoff: 100
    url: http://localhost:8113
  limits-velocity:
    retry:
      attempts: 4
      backoff: 100
    url: http://localhost:8113
  money-request:
    retry:
      attempts: 4
      backoff: 100
    url: http://localhost:8113
  payment-registration:
    retry:
      attempts: 4
      backoff: 100
    url: http://localhost:8113
spring:
  cloud:
    openfeign:
      client:
        config:
          bankAccountConnectorApiClient:
              connectTimeout: 2000
              readTimeout: 2000
          limitsVelocityApiClient:
            connectTimeout: 2000
            readTimeout: 2000
          moneyRequestApiClient:
              connectTimeout: 2000
              readTimeout: 2000
          paymentRegistrationApiClient:
            connectTimeout: 2000
            readTimeout: 2000
      httpclient:
        disableSslValidation: false
      okhttp:
        enabled: true