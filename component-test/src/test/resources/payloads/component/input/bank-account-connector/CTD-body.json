{"fi_to_fi_customer_debit_transfer": {"group_header": {"message_identification": "d04273e9014645c2b12e3ef18ef8589c", "creation_datetime": "2023-09-06T14:45:22Z", "number_of_transactions": "1", "settlement_information": {"settlement_method": "CLRG", "clearing_system": {"objectType": "proprietary", "proprietary": "ETR"}}, "instructing_agent": {"financial_institution_identification": {"clearing_system_member_identification": {"member_identification": "CA000006"}}}, "instructed_agent": {"financial_institution_identification": {"clearing_system_member_identification": {"member_identification": "NOTPROVIDED"}}}}, "direct_debit_transaction_information": [{"payment_identification": {"instruction_identification": "ZZZZ9999", "end_to_end_identification": "BBBB9999", "transaction_identification": "BBBB9999"}, "payment_type_information": {"local_instrument": {"objectType": "proprietary", "proprietary": "REGULAR_PAYMENT"}}, "interbank_settlement_amount": {"amount": 1709.66, "currency": "CAD"}, "interbank_settlement_date": "2023-09-06", "charge_bearer": "SLEV", "creditor": {"name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "contact_details": {"mobile_number": "******-222-9999"}}, "creditor_account": {"identification": {"objectType": "other", "other": {"identification": "111-19999-************", "scheme_name": {"objectType": "proprietary", "proprietary": "BANK_ACCT_NO"}}}}, "creditor_agent": {"financial_institution_identification": {"clearing_system_member_identification": {"member_identification": "NOTPROVIDED"}}}, "debtor": {"name": "<PERSON><PERSON>he Thepower", "contact_details": {"mobile_number": "******-111-9999", "email_address": "<EMAIL>"}}, "debtor_account": {"identification": {"objectType": "other", "other": {"identification": "222-19999-************", "scheme_name": {"objectType": "proprietary", "proprietary": "BANK_ACCT_NO"}}}}, "debtor_agent": {"financial_institution_identification": {"clearing_system_member_identification": {"member_identification": "NOTPROVIDED"}}}, "related_remittance_information": [], "remittance_information": {"unstructured": ["paiement de facture"], "structured": []}}], "HTTP_header": {"id": "BBBB9999", "x-c1-client-id": "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0", "x-request-id": "3ee01483-2b49-467c-8cde-c48d1a1fd1c0", "x-retry-indicator": "false"}}}