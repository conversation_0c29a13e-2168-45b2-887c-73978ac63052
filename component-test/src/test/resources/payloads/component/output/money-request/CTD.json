{"creditorPaymentActivationRequest": {"groupHeader": {"messageIdentification": "d04273e9014645c2b12e3ef18ef8589c", "creationDateTime": "2023-09-06T14:45:22Z", "numberOfTransactions": 1, "initiatingParty": {"name": "Initiating<PERSON><PERSON><PERSON>"}}, "paymentInformation": {"categoryPurpose": {"code": "INS"}, "expiryDate": "2026-05-05T17:29:12.123000Z", "moneyRequestStatus": "AVAILABLE_TO_BE_FULFILLED", "paymentCondition": {"amountModificationAllowed": false}, "debtor": {"name": "<PERSON><PERSON>he Thepower"}, "creditTransferTransaction": {"payment_identification": {"instruction_identification": "string", "end_to_end_identification": "string"}, "amount": {"instructedAmount": 1709.66, "currency": "CAD"}, "creditorAgent": {"financialInstitutionIdentification": {"clearingSystemMemberIdentification": {"memberIdentification": "NOTPROVIDED"}}}, "creditor": {"name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "contactDetails": {"mobileNumber": "******-222-9999"}}, "remittanceInformation": {"unstructured": ["paiement de facture"], "structured": []}, "invoice": {"type": {"code": "CINV", "identification": "identification"}, "issueDate": "2023-09-06T14:45:22Z"}}}}}