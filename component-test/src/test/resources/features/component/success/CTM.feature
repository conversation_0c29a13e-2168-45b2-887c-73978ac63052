Feature: List of component test scenarios for the use case simulate domestic outgoing payment(CTMx),
  Documentation: https://wiki.bnc.ca/x/i87efg

  Scenario: CT12 - Happy path
    Given a payment simulation requested with
      | endToEndBusinessIdentification | 5a6fad7df3fe4e3481a096f48c818ab3 |
      | headers                        | CTM/CTM12-header.json            |
      | body                           | CTM/CTM12-body.json              |
    And the system dates are set to
      | 2023-09-06T14:45:57.923Z |
    And ok<PERSON> responds with scopes and statuses
      | scope                    | status |
      | paymentRegistrationScope | 201    |
      | limitsVelocityScope      | 201    |
    And the following API simulations are configured
      | apiType              | operation          | headers                                   | body                                   | pathParam                           | response                            |
      | paymentRegistration  | getRegistrations   | payment-registration/CT-headers.json      |                                        |                                     | 200:payment-registration/CTM12.json |
      | limitsVelocity       | simulateVelocities | limits-velocity/CTM12-headers.json        | limits-velocity/CTM12-body.json        |                                     | 204                                 |
      | bankAccountConnector | initiateDebit      | bank-account-connector/CTM12-headers.json | bank-account-connector/CTM12-body.json | id:5a6fad7df3fe4e3481a096f48c818ab3 | 201                                 |
    When the payment simulation request is processed
    Then a 202 status is received with body "null"
    And all configured API operations are called at least once
