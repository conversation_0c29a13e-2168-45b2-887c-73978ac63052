Feature: List of component test scenarios for the use case simulate domestic outgoing payment(CTAx),
  Documentation: https://wiki.bnc.ca/x/7v-2c

  Background:
    Given a payment simulation requested with
      | endToEndBusinessIdentification | 5a6fad7df3fe4e3481a096f48c818ab3 |
      | headers                        | CTA/CTA-header.json              |
      | body                           | CTA/CTA-body.json                |
    And okta responds with scopes and statuses
      | scope                    | status |
      | paymentRegistrationScope | 201    |

  Scenario: CTA1 - Payment Registration API does not reply
    Given the following API simulation is configured
      | apiType             | operation        | headers                              | response |
      | paymentRegistration | getRegistrations | payment-registration/CT-headers.json | timeout  |
    When the payment simulation request is processed
    Then a 500 status is received with body "CTA/CTA1.json"
    And the log message contains information from file "logging/CTA/CTA1_service_log.json"
    And verify the "paymentRegistration" API operation "getRegistrations" is called 4 times

  Scenario Outline: <CTA> - Payment Registration API <scenario>
    Given the following API simulation is configured
      | apiType             | operation        | headers                              | response                                                             |
      | paymentRegistration | getRegistrations | payment-registration/CT-headers.json | <payment_registration_response_code>:payment-registration/<CTA>.json |
    When the payment simulation request is processed
    Then a <response_code> status is received with body "CTA/<CTA>.json"
    And the log message contains information from file "logging/CTA/<CTA>_service_log.json"
    And verify the "paymentRegistration" API operation "getRegistrations" is called 1 times
    Examples:
      | CTA  | scenario | payment_registration_response_code | response_code |
      | CTA2 | HTTP 400 | 400                                | 400           |
      | CTA4 | HTTP 500 | 500                                | 500           |

  Scenario: CTA3 - Payment Registration API replies with HTTP 404
    Given the following API simulation is configured
      | apiType             | operation        | headers                              | response |
      | paymentRegistration | getRegistrations | payment-registration/CT-headers.json | 404      |
    When the payment simulation request is processed
    Then a 404 status is received with body "CTA/CTA3.json"
    And the log message contains information from file "logging/CTA/CTA3_service_log.json"
    And verify the "paymentRegistration" API operation "getRegistrations" is called 1 times

  Scenario: CTA5 - Payment Registration API replies with HTTP 503
    Given the following API simulation is configured
      | apiType             | operation        | headers                              | response |
      | paymentRegistration | getRegistrations | payment-registration/CT-headers.json | 503      |
    When the payment simulation request is processed
    Then a 500 status is received with body "CTA/CTA5.json"
    And the log message contains information from file "logging/CTA/CTA5_service_log.json"
    And verify the "paymentRegistration" API operation "getRegistrations" is called 4 times

  Scenario: CTA6 - Payment Registration API call but OKTA fails to return an access token
    Given okta responds with scopes and statuses
      | scope                    | status |
      | paymentRegistrationScope | 401    |
    And the following API simulation is configured
      | apiType             | operation        | headers                              | response |
      | paymentRegistration | getRegistrations | payment-registration/CT-headers.json | 200      |
    When the payment simulation request is processed
    Then a 500 status is received with body "CTA/CTA6.json"
    And the log message contains information from file "logging/CTA/CTA6_service_log.json"
    And verify the "paymentRegistration" API operation "getRegistrations" is called 0 times

  Scenario: CTA7 - Payment Registration API call replies with http 200 but client is not registered
    Given the following API simulation is configured
      | apiType             | operation        | headers                              | response                           |
      | paymentRegistration | getRegistrations | payment-registration/CT-headers.json | 200:payment-registration/CTA7.json |
    When the payment simulation request is processed
    Then a 400 status is received with body "CTA/CTA7.json"
    And the log message contains information from file "logging/CTA/CTA7_service_log.json"
    And verify the "paymentRegistration" API operation "getRegistrations" is called 1 times
