Feature: List of component test scenarios for the use case simulate domestic outgoing payment(CTMx),
  Documentation: https://wiki.bnc.ca/x/i87efg

  Scenario Outline: "<CTM>" "<description>"
    Given a payment simulation request for endToEndBusinessIdentification "5a6fad7df3fe4e3481a096f48c818ab3" with headers "<header>" and body "<body>"
    When the payment simulation request is processed
    Then a 400 status is received with body "<response>"
    And the log message contains information from file "<log>"
    Examples:
      | CTM   | body                | header                | response                | log                                | description                                                         |
      | CTM1  | CTM/CTM1-body.json  | CTM/CTM1-header.json  | CTM/CTM1-response.json  | logging/CTM/CTM1_service_log.json  | REST contract not respected by the client                           |
      | CTM2  | CTM/CTM2-body.json  | CTM/CTM2-header.json  | CTM/CTM2-response.json  | logging/CTM/CTM2_service_log.json  | we have a REGULAR_PAYMENT without AuthenticationInformation         |
      | CTM3  | CTM/CTM3-body.json  | CTM/CTM3-header.json  | CTM/CTM3-response.json  | logging/CTM/CTM3_service_log.json  | we have a ACCOUNT_ALIAS_PAYMENT without AuthenticationInformation   |
      | CTM4  | CTM/CTM4-body.json  | CTM/CTM4-header.json  | CTM/CTM4-response.json  | logging/CTM/CTM4_service_log.json  | Deferred Payment without all required information                   |
      | CTM5  | CTM/CTM5-body.json  | CTM/CTM5-header.json  | CTM/CTM5-response.json  | logging/CTM/CTM5_service_log.json  | creditorName is missing                                             |
      | CTM6  | CTM/CTM6-body.json  | CTM/CTM6-header.json  | CTM/CTM6-response.json  | logging/CTM/CTM6_service_log.json  | creditorLanguage is missing                                         |
      | CTM7  | CTM/CTM7-body.json  | CTM/CTM7-header.json  | CTM/CTM7-response.json  | logging/CTM/CTM7_service_log.json  | but we have both phoneNumber and EmailAddress                       |
      | CTM8  | CTM/CTM8-body.json  | CTM/CTM8-header.json  | CTM/CTM8-response.json  | logging/CTM/CTM8_service_log.json  | we have neither phoneNumber nor EmailAddress                        |
      | CTM9  | CTM/CTM9-body.json  | CTM/CTM9-header.json  | CTM/CTM9-response.json  | logging/CTM/CTM9_service_log.json  | we have malicious characters in unstructuredData                    |
      | CTM10 | CTM/CTM10-body.json | CTM/CTM10-header.json | CTM/CTM10-response.json | logging/CTM/CTM10_service_log.json | creditorId is missing for ORGANISATION                              |
      | CTM11 | CTM/CTM11-body.json | CTM/CTM11-header.json | CTM/CTM11-response.json | logging/CTM/CTM11_service_log.json | Auto-Deposit mandatory field is missing                             |
      | CTM13 | CTM/CTM13-body.json | CTM/CTM13-header.json | CTM/CTM13-response.json | logging/CTM/CTM13_service_log.json | we have a FULFILL_REQUEST_FOR_PAYMENT without interacMoneyRequestId |
