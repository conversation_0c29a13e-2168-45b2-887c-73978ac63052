Feature: List of component test scenarios for the use case simulate domestic outgoing payment(CTDx),
  Documentation: https://wiki.bnc.ca/x/2w0FiQ

  Background:
    Given a payment simulation requested with
      | endToEndBusinessIdentification | BBBB9999            |
      | headers                        | CTD/CTD-header.json |
      | body                           | CTD/CTD-body.json   |
    And the system dates are set to
      | 2023-09-06T14:45:57.923Z |
    And <PERSON><PERSON> responds with scopes and statuses
      | scope                    | status |
      | paymentRegistrationScope | 201    |
      | limitsVelocityScope      | 201    |
    And the following API simulations are configured
      | apiType             | operation               | headers                              | body                          | response                          | pathParam                      |
      | limitsVelocity      | simulateVelocities      | limits-velocity/CTD-headers.json     | limits-velocity/CTD-body.json | 204                               |                                |
      | moneyRequest        | getIncomingMoneyRequest | money-request/CTD-headers.json       |                               | 200:money-request/CTD.json        | interacMoneyRequestId:DDDD9999 |
      | paymentRegistration | getRegistrations        | payment-registration/CT-headers.json |                               | 200:payment-registration/CTD.json |                                |

  Scenario: CTD1 - Bank Account Connector API does not reply
    Given the following API simulation is configured
      | apiType              | operation     | headers                                 | body                                 | response | pathParam   |
      | bankAccountConnector | initiateDebit | bank-account-connector/CTD-headers.json | bank-account-connector/CTD-body.json | timeout  | id:BBBB9999 |
    When the payment simulation request is processed
    Then a 500 status is received with body "CTD/CTD1.json"
    And the log message contains information from file "logging/CTD/CTD1_service_log.json"
    And verify the "bankAccountConnector" API operation "initiateDebit" with params "id:BBBB9999" is called 3 times

  Scenario Outline: <CTD> - Bank Account Connector API return <scenario>
    Given the following API simulation is configured
      | apiType              | operation     | headers                                 | body                                 | response                                              | pathParam   |
      | bankAccountConnector | initiateDebit | bank-account-connector/CTD-headers.json | bank-account-connector/CTD-body.json | <bac_response_code>:bank-account-connector/<CTD>.json | id:BBBB9999 |
    When the payment simulation request is processed
    Then a <response_code> status is received with body "CTD/<CTD>.json"
    And the log message contains information from file "logging/CTD/<CTD>_service_log.json"
    And verify the "bankAccountConnector" API operation "initiateDebit" with params "id:BBBB9999" is called <number_of_calls> times
    Examples:
      | CTD  | scenario | bac_response_code | response_code | number_of_calls |
      | CTD2 | HTTP 400 | 400               | 400           | 1               |
      | CTD3 | HTTP 500 | 500               | 500           | 1               |
      | CTD4 | HTTP 500 | 503               | 500           | 3               |