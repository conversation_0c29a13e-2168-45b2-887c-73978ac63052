Feature: List of component test scenarios for the use case simulate domestic outgoing payment(CTCx),
  Documentation: https://wiki.bnc.ca/x/1wopi

  Background:
    Given a payment simulation requested with
      | endToEndBusinessIdentification | 5a6fad7df3fe4e3481a096f48c818ab3 |
      | headers                        | CTC/CTC-header.json              |
      | body                           | CTC/CTC-body.json                |
    And the system dates are set to
      | 2023-09-06T14:45:57.923Z |
    And <PERSON><PERSON> responds with scopes and statuses
      | scope                    | status |
      | paymentRegistrationScope | 201    |
      | limitsVelocityScope      | 201    |
    And the following API simulations are configured
      | apiType             | operation          | headers                              | body                          | response                          |
      | paymentRegistration | getRegistrations   | payment-registration/CT-headers.json |                               | 200:payment-registration/CTC.json |
      | limitsVelocity      | simulateVelocities | limits-velocity/CTC-headers.json     | limits-velocity/CTC-body.json | 204                               |


  Scenario: CTC1 - Money Request API does not reply
    Given the following API simulation is configured
      | apiType      | operation               | headers                        | pathParam                      | response |
      | moneyRequest | getIncomingMoneyRequest | money-request/CTC-headers.json | interacMoneyRequestId:DDDD9999 | timeout  |
    When the payment simulation request is processed
    Then a 500 status is received with body "CTC/CTC1.json"
    And the log message contains information from file "logging/CTC/CTC1_service_log.json"
    And verify the "paymentRegistration" API operation "getRegistrations" is called 1 times
    And verify the "limitsVelocity" API operation "simulateVelocities" is called 1 times
    And verify the "moneyRequest" API operation "getIncomingMoneyRequest" with params "interacMoneyRequestId:DDDD9999" is called 4 times


  Scenario Outline: <CTC> - Money Request API <scenario>
    Given the following API simulation is configured
      | apiType      | operation               | headers                        | pathParam                      | response                                               |
      | moneyRequest | getIncomingMoneyRequest | money-request/CTC-headers.json | interacMoneyRequestId:DDDD9999 | <money_request_response_code>:money-request/<CTC>.json |
    When the payment simulation request is processed
    Then a <response_code> status is received with body "CTC/<CTC>.json"
    And the log message contains information from file "logging/CTC/<CTC>_service_log.json"
    And verify the "paymentRegistration" API operation "getRegistrations" is called 1 times
    And verify the "limitsVelocity" API operation "simulateVelocities" is called 1 times
    And verify the "moneyRequest" API operation "getIncomingMoneyRequest" with params "interacMoneyRequestId:DDDD9999" is called 1 times
    Examples:
      | CTC  | scenario | money_request_response_code | response_code |
      | CTC2 | HTTP 400 | 400                         | 400           |
      | CTC3 | HTTP 500 | 500                         | 500           |
