Feature: List of component test scenarios for the use case simulate domestic outgoing payment(CTBx),
  Documentation: https://wiki.bnc.ca/x/l87efg

  Background:
    Given a payment simulation requested with
      | endToEndBusinessIdentification | 5a6fad7df3fe4e3481a096f48c818ab3 |
      | headers                        | CTB/CTB-header.json              |
      | body                           | CTB/CTB-body.json                |
    And the system dates are set to
      | 2023-09-06T14:45:57.923Z |
    And <PERSON><PERSON> responds with scopes and statuses
      | scope                    | status |
      | paymentRegistrationScope | 201    |
      | limitsVelocityScope      | 201    |
    And the following API simulations are configured
      | apiType             | operation        | headers                              | response                          |
      | paymentRegistration | getRegistrations | payment-registration/CT-headers.json | 200:payment-registration/CTB.json |

  Scenario: CTB1 - Limits Velocity API does not reply
    Given the following API simulation is configured
      | apiType        | operation          | headers                          | body                          | response |
      | limitsVelocity | simulateVelocities | limits-velocity/CTB-headers.json | limits-velocity/CTB-body.json | timeout  |
    When the payment simulation request is processed
    Then a 500 status is received with body "CTB/CTB1.json"
    And the log message contains information from file "logging/CTB/CTB1_service_log.json"
    And verify the "paymentRegistration" API operation "getRegistrations" is called 1 times
    And verify the "limitsVelocity" API operation "simulateVelocities" is called 4 times

  Scenario Outline: <CTB> - Limits Velocity API <scenario>
    Given the following API simulation is configured
      | apiType        | operation          | headers                          | body                          | response                                                   |
      | limitsVelocity | simulateVelocities | limits-velocity/CTB-headers.json | limits-velocity/CTB-body.json | <limits_velocity_response_code>:limits-velocity/<CTB>.json |
    When the payment simulation request is processed
    Then a <response_code> status is received with body "CTB/<CTB>.json"
    And the log message contains information from file "logging/CTB/<CTB>_service_log.json"
    And verify the "paymentRegistration" API operation "getRegistrations" is called 1 times
    And verify the "limitsVelocity" API operation "simulateVelocities" is called 1 times
    Examples:
      | CTB  | scenario | limits_velocity_response_code | response_code |
      | CTB2 | HTTP 400 | 400                           | 400           |
      | CTB3 | HTTP 500 | 500                           | 500           |

  Scenario: CTB4 - call Limits Velocity API but okta return an error
    Given a payment simulation requested with
      | endToEndBusinessIdentification | 5a6fad7df3fe4e3481a096f48c818ab3 |
      | headers                        | CTB/CTB-header.json              |
      | body                           | CTB/CTB-body.json                |
    And okta responds with scopes and statuses
      | scope                    | status |
      | paymentRegistrationScope | 201    |
      | limitsVelocityScope      | 401    |
    And the following API simulation is configured
      | apiType             | operation        | headers                              | response                          |
      | paymentRegistration | getRegistrations | payment-registration/CT-headers.json | 200:payment-registration/CTB.json |
    When the payment simulation request is processed
    Then a 500 status is received with body "CTB/CTB4.json"
    And the log message contains information from file "logging/CTB/CTB4_service_log.json"
    And verify the "paymentRegistration" API operation "getRegistrations" is called 1 times
    And verify the "limitsVelocity" API operation "simulateVelocities" is called 0 times
