package ca.bnc.payment.acceptance.component.common;

import lombok.NoArgsConstructor;

@NoArgsConstructor
public final class ApiTestConstants {
    public static final String DATA_DIRECTORY_REQUEST_PATH = "payloads/component/input/";
    public static final String DATA_DIRECTORY_RESPONSE_PATH = "payloads/component/output/";
    public static final String SIMULATE_DOMESTIC_OUTGOING_PAYMENT_PATH = "/et_pmt_proc/etransfer-payment-processing-simulation/%s";
    public static final String TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9";
    public static final Integer TIMEOUT_DELAY_MS = 4000;
}
