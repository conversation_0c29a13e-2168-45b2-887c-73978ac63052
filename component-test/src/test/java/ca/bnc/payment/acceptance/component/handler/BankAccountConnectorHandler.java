package ca.bnc.payment.acceptance.component.handler;

import ca.bnc.payment.acceptance.component.common.ApiOperationConfig;

import java.util.Map;

public record BankAccountConnectorHandler() implements ApiOperationHandler {
    private static final String POST_BANK_ACCOUNT_CONNECTOR_ENDPOINT = "/payments/{id}/debit";
    private static final Map<String, ApiOperationConfig> OPERATIONS = Map.of(
            "initiateDebit",
            new ApiOperationConfig(
                    "initiateDebit",
                    POST_BANK_ACCOUNT_CONNECTOR_ENDPOINT,
                    "POST",
                    true,
                    false,
                    true
            )
    );

    @Override
    public ApiOperationConfig getOperationConfig(String operation) {
        return OPERATIONS.get(operation);
    }

    @Override
    public boolean supportsOperation(String operation) {
        return OPERATIONS.containsKey(operation);
    }
}
