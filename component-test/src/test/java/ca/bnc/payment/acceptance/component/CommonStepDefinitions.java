package ca.bnc.payment.acceptance.component;

import ca.bnc.payment.acceptance.component.common.ApiOperationConfig;
import ca.bnc.payment.acceptance.component.common.ApiSimulationConfig;
import ca.bnc.payment.acceptance.component.common.ApiType;
import ca.bnc.payment.acceptance.component.handler.ApiOperationHandler;
import ca.bnc.payment.acceptance.utils.FileToStringConverter;
import ca.bnc.payment.acceptance.utils.LoggerTestUtil;
import ca.bnc.payment.acceptance.utils.LoggingEventsUtil;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Errors;
import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.matching.RequestPatternBuilder;
import io.cucumber.java.After;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.io.IOException;
import java.time.Clock;
import java.time.Instant;
import java.util.List;
import java.util.Map;

import static ca.bnc.payment.acceptance.component.common.ApiTestConstants.DATA_DIRECTORY_REQUEST_PATH;
import static ca.bnc.payment.acceptance.component.common.ApiTestConstants.DATA_DIRECTORY_RESPONSE_PATH;
import static ca.bnc.payment.acceptance.component.common.ApiTestConstants.SIMULATE_DOMESTIC_OUTGOING_PAYMENT_PATH;
import static ca.bnc.payment.acceptance.component.common.ApiTestConstants.TOKEN;
import static com.github.tomakehurst.wiremock.client.WireMock.deleteRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.getRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.putRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@RequiredArgsConstructor
public class CommonStepDefinitions {

    private final ListAppender<ILoggingEvent> loggingEventListAppender = LoggerTestUtil.loggingEventListAppender;

    private final Clock clock;
    private final MockMvc mockMvc;
    private final ObjectMapper objectMapper;
    private final OktaClientTokenManager oktaClientTokenManagerMock;
    private final LoggingEventsUtil loggingEventsUtil;

    private String endToEndBusinessIdentification;
    private String requestBody;
    private HttpHeaders requestHeader;
    private ResultActions response;
    private final WireMockServer wireMockServer;

    private final ApiHandlerFactory apiHandlerFactory;
    private final ApiSimulationService apiSimulationService;
    private final List<ApiSimulationConfig> configuredApiSimulations = new java.util.ArrayList<>();


    @After
    public void tearDown() {
        loggingEventListAppender.list.clear();
        wireMockServer.resetAll();
        apiSimulationService.clearHeaders();
        configuredApiSimulations.clear();
    }

    @Given("a payment simulation request for endToEndBusinessIdentification {string} with headers {string} and body {string}")
    public void simulateDomesticOutgoingPaymentRequestWithBody(final String endToEndBusinessIdentification, final String headersFilename,
                                                               final String bodyFilename) throws IOException {
        this.endToEndBusinessIdentification = endToEndBusinessIdentification;
        this.requestHeader = FileToStringConverter.getContentAsHttpHeaders(DATA_DIRECTORY_REQUEST_PATH + headersFilename);
        this.requestBody = FileToStringConverter.getContentString(DATA_DIRECTORY_REQUEST_PATH + bodyFilename);
    }

    @Given("the system dates are set to")
    public void theSystemDatesAreSetTo(final List<String> systemDates) {
        List<Instant> instants = systemDates.stream().map(Instant::parse).toList();
        when(clock.instant()).thenReturn(instants.get(0));
    }

    @Given("okta responds with scopes and statuses")
    public void oktaRespondsWithScopesAndStatuses(final Map<String, String> scopeStatusMap) {
        when(oktaClientTokenManagerMock.getAccessToken(any()))
                .thenAnswer(invocation -> {
                    String scope = invocation.getArgument(0, String.class);
                    String status = scopeStatusMap.getOrDefault(scope, "400");
                    return "201".equals(status) ? TOKEN : null;
                });
    }

    @When("the payment simulation request is processed")
    public void theSimulateDomesticOutgoingPaymentRequestIsProcessed() throws Exception {
        response = mockMvc.perform(MockMvcRequestBuilders
                .put(String.format(SIMULATE_DOMESTIC_OUTGOING_PAYMENT_PATH, endToEndBusinessIdentification))
                .headers(requestHeader)
                .contentType("application/vnd.ca.bnc.pmt+json")
                .content(requestBody));
    }

    @Then("a {int} status is received with body {string}")
    public void aStatusIsReceivedWithBody(final int status, final String body) throws IOException {
        if (!"null".equals(body)) {
            String expectedBody = FileToStringConverter.getContentString(DATA_DIRECTORY_RESPONSE_PATH + body);
            assertThat(objectMapper.readValue(response.andReturn().getResponse().getContentAsString(), Errors.class))
                    .isEqualTo(objectMapper.readValue(expectedBody, Errors.class));
        }
        assertThat(response.andReturn().getResponse().getStatus()).isEqualTo(status);
    }

    @Then("the log message contains information from file {string}")
    public void theLogMessageContainsInformationFromFile(final String fileName) {
        assertThat(loggingEventListAppender.list).anySatisfy(iLoggingEvent ->
            loggingEventsUtil.validateLogMessageContains(DATA_DIRECTORY_RESPONSE_PATH, fileName));
    }

    @Given("a payment simulation requested with")
    public void aPaymentSimulationRequestWith(final Map<String, String> requestData) throws IOException {
        assertThat(requestData).containsOnlyKeys("endToEndBusinessIdentification", "headers", "body");
        this.endToEndBusinessIdentification = requestData.get("endToEndBusinessIdentification");
        this.requestHeader = FileToStringConverter.getContentAsHttpHeaders(DATA_DIRECTORY_REQUEST_PATH + requestData.get("headers"));
        this.requestBody = FileToStringConverter.getContentString(DATA_DIRECTORY_REQUEST_PATH + requestData.get("body"));
    }

    @Given("the following API simulation(s) is/are configured")
    public void theApiSimulationsAreConfigured(final List<Map<String, String>> apiSimulations) throws IOException {
        for (Map<String, String> apiConfigMap : apiSimulations) {
            ApiSimulationConfig apiConfig = mapToApiSimulationConfig(apiConfigMap);
            apiSimulationService.configureApiSimulation(apiConfig);
            configuredApiSimulations.add(apiConfig);
        }
    }

    @Then("verify the {string} API operation {string} is called {int} times")
    public void verifyApiOperationIsCalledNTimes(final String apiTypeName, final String operation, int times) {
        verifyApiOperationWithParams(apiTypeName, operation, Map.of(), times);
    }

    @Then("verify the {string} API operation {string} with params {string} is called {int} times")
    public void verifyApiOperationWithParamsIsCalledNTimes(final String apiTypeName, final String operation, final String paramsString,
                                                           final int times) {
        Map<String, String> pathParams = parsePathParams(paramsString);
        verifyApiOperationWithParams(apiTypeName, operation, pathParams, times);
    }

    @Then("Verify API Operations called as expected")
    public void allConfiguredApiOperationsAreCalledAtLeastOnce() {
        for (ApiSimulationConfig config : configuredApiSimulations) {
            String apiTypeName = config.apiType().getValue();
            String operation = config.operation();
            Map<String, String> pathParams = parsePathParams(config.pathParam());
            verifyApiOperationWithParams(apiTypeName, operation, pathParams, 1);
        }
    }

    private void verifyApiCall(final RequestPatternBuilder verification, final String apiType, final int times) {
        Map<String, String> headers = apiSimulationService.getApiHeaders(apiType);
        if (headers != null) {
            headers.forEach((key, value) -> verification.withHeader(key, equalTo(value)));
        }
        wireMockServer.verify(times, verification);
    }

    private Map<String, String> parsePathParams(final String paramsString) {
        return apiSimulationService.parseKeyValuePairs(paramsString);
    }

    private void verifyApiOperationWithParams(final String apiTypeName, final String operation, final Map<String, String> pathParams,
                                              final int times) {
        ApiType apiType = ApiType.fromValue(apiTypeName);
        ApiOperationHandler handler = apiHandlerFactory.getHandler(apiType);
        ApiOperationConfig config = handler.getOperationConfig(operation);

        String endpoint = buildEndpointForVerification(config.endpoint(), pathParams);
        RequestPatternBuilder verification = createVerificationBuilder(config.httpMethod(), endpoint);
        verifyApiCall(verification, apiTypeName, times);
    }

    private String buildEndpointForVerification(final String endpoint, final Map<String, String> pathParams) {
        String finalEndpoint = endpoint;
        for (Map.Entry<String, String> entry : pathParams.entrySet()) {
            String placeholder = "{" + entry.getKey() + "}";
            finalEndpoint = finalEndpoint.replace(placeholder, entry.getValue());
        }
        return finalEndpoint;
    }

    private RequestPatternBuilder createVerificationBuilder(final String httpMethod, final String endpoint) {
        return switch (httpMethod.toUpperCase()) {
            case "GET" -> getRequestedFor(urlPathEqualTo(endpoint));
            case "POST" -> postRequestedFor(urlPathEqualTo(endpoint));
            case "PUT" -> putRequestedFor(urlPathEqualTo(endpoint));
            case "DELETE" -> deleteRequestedFor(urlPathEqualTo(endpoint));
            default -> throw new IllegalArgumentException("Unsupported HTTP method for verification: " + httpMethod);
        };
    }

    private ApiSimulationConfig mapToApiSimulationConfig(final Map<String, String> map) {
        ApiType apiType = ApiType.fromValue(map.get("apiType"));
        String operation = map.get("operation");
        String endpoint = map.get("endpoint");
        String method = map.get("method");
        String body = map.get("body");
        String response = map.get("response");
        String headers = map.get("headers");
        String pathParam = map.get("pathParam");
        String queryParam = map.get("queryParam");
        return new ApiSimulationConfig(apiType, operation, endpoint, method, headers, body, response, pathParam, queryParam);
    }
}