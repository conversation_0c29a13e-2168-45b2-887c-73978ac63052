package ca.bnc.payment.acceptance.component.common;

import ca.bnc.payment.Application;
import io.cucumber.spring.CucumberContextConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

@CucumberContextConfiguration
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, classes = Application.class)
@ActiveProfiles({"test"})
@AutoConfigureMockMvc
@TestPropertySource(properties = {"SPRING_PROFILE_INCLUDE=logging"})
@Import({ComponentTestConfig.class})
public class CucumberSpringConfig {
}
