package ca.bnc.payment.acceptance.component;

import ca.bnc.payment.acceptance.component.common.ApiOperationConfig;
import ca.bnc.payment.acceptance.component.common.ApiSimulationConfig;
import ca.bnc.payment.acceptance.component.common.ApiType;
import ca.bnc.payment.acceptance.component.handler.ApiOperationHandler;
import ca.bnc.payment.acceptance.utils.FileToStringConverter;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.MappingBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static ca.bnc.payment.acceptance.component.common.ApiTestConstants.DATA_DIRECTORY_REQUEST_PATH;
import static ca.bnc.payment.acceptance.component.common.ApiTestConstants.DATA_DIRECTORY_RESPONSE_PATH;
import static ca.bnc.payment.acceptance.component.common.ApiTestConstants.TIMEOUT_DELAY_MS;
import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.delete;
import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.put;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;

@Service
@RequiredArgsConstructor
public class ApiSimulationService {
    private final ApiHandlerFactory apiHandlerFactory;
    private final ObjectMapper objectMapper;
    private final WireMockServer wireMockServer;
    private final Map<String, Map<String, String>> headersMap = new HashMap<>();

    public void configureApiSimulation(final ApiSimulationConfig apiConfig) throws IOException {
        ApiType apiType = apiConfig.apiType();
        String operation = apiConfig.operation();
        String headers = apiConfig.headers();
        String body = apiConfig.body();
        String response = apiConfig.response();
        String queryParam = apiConfig.queryParam();

        ApiOperationHandler handler = apiHandlerFactory.getHandler(apiType);

        if (!handler.supportsOperation(operation)) {
            throw new IllegalArgumentException("Operation " + operation + " not supported for API type " + apiType);
        }

        ApiOperationConfig operationConfig = handler.getOperationConfig(operation);
        String endpoint = buildEndpoint(operationConfig.endpoint(), parsePathParams(apiConfig.pathParam()), queryParam);

        MappingBuilder requestBuilder = createRequestBuilder(operationConfig.httpMethod(), endpoint);

        if (operationConfig.requiresBody() && body != null) {
            requestBuilder.withRequestBody(equalToJson(FileToStringConverter.getContentString(DATA_DIRECTORY_REQUEST_PATH + body)));
        }

        applyHeaders(requestBuilder, headers, apiType.name());
        createStubResponse(requestBuilder, response);
    }

    private Map<String, String> parsePathParams(String paramsString) {
        return parseKeyValuePairs(paramsString);
    }


    private String buildEndpoint(final String endpoint, final Map<String, String> pathParams, final String queryParam) {
        String endpointWithPathParams = Optional.ofNullable(pathParams)
                .filter(params -> !params.isEmpty())
                .map(params -> params.entrySet().stream()
                        .filter(entry -> endpoint.contains("{" + entry.getKey() + "}"))
                        .reduce(endpoint,
                                (acc, entry) -> acc.replace("{" + entry.getKey() + "}", entry.getValue()),
                                (s1, s2) -> s2))
                .orElse(endpoint);

        return Optional.ofNullable(queryParam)
                .filter(param -> !param.isEmpty())
                .map(param -> endpointWithPathParams + "?" + param)
                .orElse(endpointWithPathParams);
    }

    private MappingBuilder createRequestBuilder(final String httpMethod, final String endpoint) {
        // Split endpoint and query parameters for proper matching
        String[] parts = endpoint.split("\\?", 2);
        String path = parts[0];

        MappingBuilder builder = switch (httpMethod.toUpperCase()) {
            case "GET" -> get(urlPathEqualTo(path));
            case "POST" -> post(urlPathEqualTo(path));
            case "PUT" -> put(urlPathEqualTo(path));
            case "DELETE" -> delete(urlPathEqualTo(path));
            default -> throw new IllegalArgumentException("Unsupported HTTP method: " + httpMethod);
        };

        // Add query parameters if they exist
        if (parts.length > 1) {
            Arrays.stream(parts[1].split("&"))
                    .map(param -> param.split("=", 2))
                    .filter(kv -> kv.length == 2)
                    .forEach(kv -> builder.withQueryParam(kv[0], equalTo(kv[1])));
        }

        return builder;
    }


    private void applyHeaders(final MappingBuilder requestBuilder, final String requestHeadersPath, final String apiType) throws IOException {
        if (requestHeadersPath != null) {
            Map<String, String> headers = objectMapper.readValue(
                    FileToStringConverter.getContentString(DATA_DIRECTORY_REQUEST_PATH + requestHeadersPath),
                    new TypeReference<>() {
                    });
            headers.forEach((key, value) -> requestBuilder.withHeader(key, equalTo(value)));
            headersMap.put(apiType, headers);
        }
    }

    private void createStubResponse(final MappingBuilder requestBuilder, final String responseCodeAndResponseBody) throws IOException {
        if ("timeout".equals(responseCodeAndResponseBody)) {
            wireMockServer.stubFor(requestBuilder.willReturn(aResponse()
                    .withStatus(408)
                    .withFixedDelay(TIMEOUT_DELAY_MS)
                    .withHeader("Content-Type", "application/json")
                    .withBody("{\"error\":\"timeout\"}")));
            return;
        }

        String[] codeAndPath = responseCodeAndResponseBody.split(":", 2);
        String responseCode = codeAndPath[0];
        String responseBodyPath = codeAndPath.length > 1 ? codeAndPath[1] : null;
        String responseBody = responseBodyPath == null ? "{}" :
                FileToStringConverter.getContentString(DATA_DIRECTORY_RESPONSE_PATH + responseBodyPath);

        wireMockServer.stubFor(requestBuilder.willReturn(aResponse()
                .withStatus(Integer.parseInt(responseCode))
                .withHeader("Content-Type", "application/json")
                .withBody(responseBody)));
    }

    public Map<String, String> parseKeyValuePairs(String input) {
        if (input == null || input.isBlank()) {
            return new HashMap<>();
        }
        return Arrays.stream(input.split(","))
                .map(pair -> pair.split(":", 2))
                .peek(kv -> {
                    if (kv.length != 2) {
                        throw new IllegalArgumentException("Invalid params format: " + Arrays.toString(kv));
                    }
                })
                .collect(HashMap::new, (map, kv) -> map.put(kv[0].trim(), kv[1].trim()), HashMap::putAll);
    }

    public Map<String, String> getApiHeaders(final String apiType) {
        return headersMap.get(apiType);
    }

    public void clearHeaders() {
        headersMap.clear();
    }
}
