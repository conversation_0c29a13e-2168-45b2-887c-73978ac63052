package ca.bnc.payment.acceptance.component.common;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WireMockConfig {
    private static final WireMockServer wireMockServer = new WireMockServer(
            WireMockConfiguration.options().bindAddress("127.0.0.1").port(8113)
    );

    static {
        if (!wireMockServer.isRunning()) {
            wireMockServer.start();
        }
    }

    @Bean(destroyMethod = "stop")
    public WireMockServer wireMockServer() {
        return wireMockServer;
    }
}

