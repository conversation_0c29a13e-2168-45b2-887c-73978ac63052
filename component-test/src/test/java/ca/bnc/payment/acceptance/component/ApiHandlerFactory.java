package ca.bnc.payment.acceptance.component;

import ca.bnc.payment.acceptance.component.common.ApiType;
import ca.bnc.payment.acceptance.component.handler.ApiOperationHandler;
import ca.bnc.payment.acceptance.component.handler.BankAccountConnectorHandler;
import ca.bnc.payment.acceptance.component.handler.LimitsVelocityHandler;
import ca.bnc.payment.acceptance.component.handler.MoneyRequestHandler;
import ca.bnc.payment.acceptance.component.handler.PaymentRegistrationHandler;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

@Component
public class ApiHandlerFactory {
    private final Map<ApiType, ApiOperationHandler> handlers = new EnumMap<>(ApiType.class);

    @PostConstruct
    public void initialize() {
        handlers.put(ApiType.LIMITS_VELOCITY, new LimitsVelocityHandler());
        handlers.put(ApiType.MONEY_REQUEST, new MoneyRequestHandler());
        handlers.put(ApiType.PAYMENT_REGISTRATION, new PaymentRegistrationHandler());
        handlers.put(ApiType.BANK_ACCOUNT_CONNECTOR, new BankAccountConnectorHandler());
    }

    public ApiOperationHandler getHandler(ApiType apiType) {
        return handlers.get(apiType);
    }
}
