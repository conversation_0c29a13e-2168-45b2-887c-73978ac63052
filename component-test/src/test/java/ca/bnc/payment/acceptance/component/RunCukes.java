package ca.bnc.payment.acceptance.component;

import io.cucumber.junit.Cucumber;
import io.cucumber.junit.CucumberOptions;
import org.junit.runner.RunWith;

@RunWith(Cucumber.class)
@CucumberOptions(
        features = {"src/test/resources/features/component"},
        stepNotifications = true,
        extraGlue = "ca.bnc.payment.acceptance.commons",
        plugin = {"pretty",
                "html:target/cucumber",
                "json:target/cucumber.json"},
        tags = "not @ignore")
public class RunCukes {
}
