package ca.bnc.payment.acceptance.component.common;

import ca.bnc.payment.acceptance.utils.LoggingEventsUtil;
import ca.bnc.payment.utils.IdGenerator;
import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import org.mockito.AdditionalAnswers;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.time.Clock;

import static org.mockito.Mockito.mock;

@TestConfiguration
public class ComponentTestConfig {

    @Bean
    @Primary
    public Clock mockClock() {
        return mock(Clock.class, AdditionalAnswers.delegatesTo(Clock.systemUTC()));
    }

    @Primary
    @Bean
    public IdGenerator mockIdGenerator() {
        return mock(IdGenerator.class);
    }

    @Bean
    @Primary
    public OktaClientTokenManager oktaClientTokenManagerMock() {
        return mock(OktaClientTokenManager.class);
    }

    @Bean
    public LoggingEventsUtil loggingEventsUtil() {
        return new LoggingEventsUtil();
    }
}
