package ca.bnc.payment.acceptance.utils;


import ch.qos.logback.classic.spi.ILoggingEvent;
import net.logstash.logback.marker.LogstashMarker;
import net.logstash.logback.marker.MapEntriesAppendingMarker;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

import static ca.bnc.payment.acceptance.utils.LoggerTestUtil.loggingEventListAppender;
import static java.lang.String.format;
import static org.assertj.core.api.Assertions.assertThat;

public class LoggingEventsUtil {

    public void assertContains(final List<ILoggingEvent> loggingEvents, final LogEntryRequirement logEntryRequirement) {
        assertThat(loggingEvents)
                .overridingErrorMessage(
                        "Expecting\n%s\nto contain\n%s\n",
                        toString(loggingEvents),
                        logEntryRequirement)
                .anySatisfy(iLoggingEvent -> {
                    assertThat(extractFormattedMessage(iLoggingEvent)).contains(logEntryRequirement.getMessage());
                    assertArguments(iLoggingEvent, logEntryRequirement);
                });
    }
    public void validateLogMessageContains(final String basePath, final String responseFileNames) {
        LogEntryRequirement logEntryRequirements =
                mapToLogEntryRequirement(basePath, responseFileNames);

        assertContains(loggingEventListAppender.list, logEntryRequirements);
    }

    private String toString(final List<ILoggingEvent> loggingEvents) {
        final StringBuffer sb = new StringBuffer("[");
        final AtomicBoolean first = new AtomicBoolean(false);
        loggingEvents.forEach(iLoggingEvent -> {
            if (first.getAndSet(true)) {
                sb.append(",\n");
            }
            sb.append(format("ILoggingEvent(message=%s, arguments=[%s])", iLoggingEvent.getFormattedMessage(), toString(iLoggingEvent)));
        });
        sb.append("]");
        return sb.toString();
    }

    private String toString(final ILoggingEvent iLoggingEvent) {
        final StringBuffer sb = new StringBuffer();
        final AtomicBoolean first = new AtomicBoolean(false);
        getFieldsMarker(iLoggingEvent)
                .map(this::getArgumentMap)
                .ifPresent(map -> toString(map, first, sb));
        return sb.toString();
    }

    private void toString(final Map<?, ?> map, final AtomicBoolean first, final StringBuffer sb) {
        map.forEach((key, value) -> {
            if (first.getAndSet(true)) {
                sb.append(", ");
            }
            sb.append(format("%s=%s", key, value));
        });
    }

    private String extractFormattedMessage(final ILoggingEvent iLoggingEvent) {
        return getFieldsMarker(iLoggingEvent)
                .map(this::extractLogMessage)
                .orElse("");
    }

    private String extractLogMessage(final MapEntriesAppendingMarker marker) {
        Map<?, ?> argumentMap = getArgumentMap(marker);
        return Optional.ofNullable(argumentMap.get("loggingMessage"))
                .map(Object::toString)
                .orElse("");
    }

    private Optional<MapEntriesAppendingMarker> getFieldsMarker(final ILoggingEvent iLoggingEvent) {
        return Arrays.stream(iLoggingEvent.getArgumentArray())
                .filter(argument -> argument instanceof MapEntriesAppendingMarker)
                .map(MapEntriesAppendingMarker.class::cast)
                .filter(marker -> MapEntriesAppendingMarker.MARKER_NAME.equals(marker.getName()))
                .findFirst();
    }

    private Map<?, ?> getArgumentMap(final MapEntriesAppendingMarker marker) {
        try {
            Field f = MapEntriesAppendingMarker.class.getDeclaredField("map");
            f.setAccessible(true);
            Map<?, ?> map = (Map<?, ?>) f.get(marker);
            f.setAccessible(false);
            return map;
        } catch (Exception e) {
            throw new RuntimeException("Error while extracting loggingMessage field", e);
        }
    }

    private void assertArguments(final ILoggingEvent iLoggingEvent, final LogEntryRequirement logEntryRequirement) {
        final String mapEntriesString = getArgumentsAsString(iLoggingEvent);
        Optional.ofNullable(logEntryRequirement.getArguments())
                .ifPresent(stringStringMap -> stringStringMap
                        .forEach((key, value) -> assertThat(mapEntriesString).contains(key + "=" + value))
                );
    }

    private String getArgumentsAsString(final ILoggingEvent iLoggingEvent) {
        return getFieldsMarker(iLoggingEvent)
                .map(LogstashMarker::toString)
                .orElse("");
    }
    private LogEntryRequirement mapToLogEntryRequirement(final String basePath, final String filename) {
        try {
            return FileToStringConverter.getContentObject(basePath + filename, LogEntryRequirement.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
