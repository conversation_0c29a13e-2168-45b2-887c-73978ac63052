package ca.bnc.payment.acceptance.utils;

import ca.bnc.payment.controller.GlobalErrorHandler;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import lombok.Getter;
import org.slf4j.LoggerFactory;
import org.springframework.util.ClassUtils;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URL;
import java.util.Arrays;
import java.util.Enumeration;

@Getter
public class LoggerTestUtil {

    private static final String BASE_PACKAGE_NAME = "ca.bnc.payment";
    public static final ListAppender<ILoggingEvent> loggingEventListAppender = LoggerTestUtil.createLoggingEventListAppender();

    private static ListAppender<ILoggingEvent> createLoggingEventListAppender() {

        try {

            ListAppender<ILoggingEvent> loggingEventListAppender = new ListAppender<>();
            loggingEventListAppender.start();

            // Add to all classes
            addAppender(loggingEventListAppender);

            addAppenders(loggingEventListAppender,
                    GlobalErrorHandler.class);

            return loggingEventListAppender;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    private static void addAppender(final ListAppender<ILoggingEvent> loggingEventListAppender) throws IOException {
        ClassLoader classLoader = ClassUtils.getDefaultClassLoader();
        String path = ClassUtils.convertClassNameToResourcePath(BASE_PACKAGE_NAME);
        Enumeration<URL> resources = classLoader.getResources(path);
        while (resources.hasMoreElements()) {
            URL resource = resources.nextElement();
            addAppender(new File(resource.getFile()), BASE_PACKAGE_NAME, loggingEventListAppender);
        }
    }

    private static void addAppender(final File directory, final String packageName, final ListAppender<ILoggingEvent> loggingEventListAppender) {
        if (directory.exists() && directory.listFiles() != null) {
            Arrays.stream(directory.listFiles())
                    .forEach(file -> {
                        if (file.isDirectory()) {
                            addAppender(file, packageName + "." + file.getName(), loggingEventListAppender);
                        } else if (file.getName().endsWith(".class")) {
                            try {
                                String className = packageName + '.' + file.getName().substring(0, file.getName().length() - 6);
                                addLogAppender(Class.forName(className), loggingEventListAppender);
                            } catch (ClassNotFoundException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    });
        }
    }

    private static void addLogAppender(final Class<?> aClass, final ListAppender<ILoggingEvent> loggingEventListAppender) {
        Arrays.stream(aClass.getFields())
                .filter(field -> org.slf4j.Logger.class.equals(field.getType()))
                .findFirst()
                .ifPresent(loggerField -> addAppender(aClass, loggingEventListAppender, loggerField));
    }

    private static void addAppender(final Class<?> aClass, final ListAppender<ILoggingEvent> loggingEventListAppender, final Field loggerField) {
        try {
            loggerField.setAccessible(true);
            Logger logger = (Logger) loggerField.get(aClass);
            logger.addAppender(loggingEventListAppender);
            loggerField.setAccessible(false);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static void addAppenders(ListAppender<ILoggingEvent> appender, Class<?>... classes) {
        for (Class<?> clazz : classes) {
            ((Logger) LoggerFactory.getLogger(clazz)).addAppender(appender);
        }
    }

}
