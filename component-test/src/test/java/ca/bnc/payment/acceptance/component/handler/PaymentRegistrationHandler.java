package ca.bnc.payment.acceptance.component.handler;

import ca.bnc.payment.acceptance.component.common.ApiOperationConfig;

import java.util.Map;


public record PaymentRegistrationHandler() implements ApiOperationHandler {
    private static final String GET_REGISTRATIONS_ENDPOINT = "/pmt_rail_net/registrations";
    private static final Map<String, ApiOperationConfig> OPERATIONS = Map.of(
            "getRegistrations",
            new ApiOperationConfig(
                    "getRegistrations",
                    GET_REGISTRATIONS_ENDPOINT,
                    "GET",
                    false,
                    false,
                    false
            )
    );

    @Override
    public ApiOperationConfig getOperationConfig(String operation) {
        return OPERATIONS.get(operation);
    }

    @Override
    public boolean supportsOperation(String operation) {
        return OPERATIONS.containsKey(operation);
    }
}
