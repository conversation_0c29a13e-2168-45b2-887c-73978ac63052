package ca.bnc.payment.acceptance.component.common;

import lombok.Getter;

@Getter
public enum ApiType {
    LIMITS_VELOCITY("limitsVelocity"),
    MONEY_REQUEST("moneyRequest"),
    PAYMENT_REGISTRATION("paymentRegistration"),
    BANK_ACCOUNT_CONNECTOR("bankAccountConnector");

    private final String value;

    ApiType(String value) {
        this.value = value;
    }

    public static ApiType fromValue(String value) {
        for (ApiType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown API type: " + value);
    }
}