package ca.bnc.payment.acceptance.component.handler;

import ca.bnc.payment.acceptance.component.common.ApiOperationConfig;

import java.util.Map;

public final class MoneyRequestHandler implements ApiOperationHandler {
    private static final String GET_MONEY_REQUEST_ENDPOINT = "/et_pmt_proc/incoming-money-request/{interacMoneyRequestId}";
    private final Map<String, ApiOperationConfig> operations = Map.of(
            "getIncomingMoneyRequest",
            new ApiOperationConfig(
                    "getIncomingMoneyRequest",
                    GET_MONEY_REQUEST_ENDPOINT,
                    "GET",
                    true,
                    false,
                    false)
    );

    @Override
    public ApiOperationConfig getOperationConfig(String operation) {
        return operations.get(operation);
    }

    @Override
    public boolean supportsOperation(String operation) {
        return operations.containsKey(operation);
    }
}
