package ca.bnc.payment.acceptance.component.handler;

import ca.bnc.payment.acceptance.component.common.ApiOperationConfig;

import java.util.Map;

public final class LimitsVelocityHandler implements ApiOperationHandler {
    private static final String POST_VELOCITIES_SIMULATION_ENDPOINT = "/velocities/simulation";
    private final Map<String, ApiOperationConfig> operations = Map.of(
            "simulateVelocities",
            new ApiOperationConfig(
                    "simulateVelocities",
                    POST_VELOCITIES_SIMULATION_ENDPOINT,
                    "POST",
                    false,
                    false,
                    true
            )
    );

    @Override
    public ApiOperationConfig getOperationConfig(String operation) {
        return operations.get(operation);
    }

    @Override
    public boolean supportsOperation(String operation) {
        return operations.containsKey(operation);
    }
}
