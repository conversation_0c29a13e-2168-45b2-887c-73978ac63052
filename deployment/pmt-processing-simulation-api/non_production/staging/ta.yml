---
api:
  name: pmt-processing-simulation-api
  service_type: business
  app_id: 6256
  helm_version: "0.2.2-rc-3"

metadata:
  appversion: $ARTIFACT_VERSION
  tool_stack_id: 318a4238-5493-4beb-97d5-950b9ef4d933
  gateways:
    - 6084-nonproduction-common-gateway/6084-nonproduction-staging-ta-ingressgw
  hostnames:
    - pmt-ta.apis.bngf.local
    - 6084-npr-staging-ta.15001.npr.aws.bngf.local

virtualService:
  hosts:
    - "pmt-ta.apis.bngf.local"
    - "6084-npr-staging-ta.15001.npr.aws.bngf.local"
  gateways:
    - "6084-nonproduction-common-gateway/6084-nonproduction-staging-ta-ingressgw"
  http:
    - match:
        - uri:
            regex: "/et_pmt_proc/etransfer-payment-processing-simulation/[a-zA-Z0-9-]+"
            method:
              exact: PUT
      route:
        - destination:
            host: "pmt-processing-simulation-api.6256-nonproduction-staging-ta.svc.cluster.local"
            port:
              number: 8080

deployment:
  name: pmt-processing-simulation-api
  api_operation_tools: customer-application
  environment: non_production
  stage: ta
  replica_count: 2
  imagePullSecrets:
    - name: 318a4238-5493-4beb-97d5-950b9ef4d933.artifact-management
  k8s:
    podsecuritycontext:
      runAsUser: 1001
    annotations_pod:
      sidecar.istio.io/proxyCPU: "100m"
      sidecar.istio.io/proxyCPULimit: "2000m"
      sidecar.istio.io/proxyMemory: "192Mi"
      sidecar.istio.io/proxyMemoryLimit: "2048Mi"
  hpa:
    min_replicas: 2
    max_replicas: 2
    target_cpu_utilization_percentage: 100

containers:
  - name: pmt-processing-simulation-api
    image:
      name: $DEPLOY_IMAGE_FULL_NAME
      pullPolicy: Always
    configuration_required: true
    ports:
      - container_port: 8080
        exposed_port: 8080
        name: HTTP
        protocol: TCP
        service_mapping:
          enabled: true
    k8s:
      resources:
        limits:
          cpu: 2000m
          memory: 2000Mi
        requests:
          cpu: 300m
          memory: 1000Mi
      startupProbe:
        initialDelaySeconds: 15
        timeoutSeconds: 5
        periodSeconds: 5
        successThreshold: 1
        failureThreshold: 15
        http:
          port: 9999
          path: /actuator/health/liveness
      livenessProbe:
        initialDelaySeconds: 15
        timeoutSeconds: 5
        periodSeconds: 5
        successThreshold: 1
        failureThreshold: 15
        http:
          port: 9999
          path: /actuator/health/liveness
      readinessProbe:
        initialDelaySeconds: 15
        timeoutSeconds: 5
        periodSeconds: 5
        successThreshold: 1
        failureThreshold: 15
        http:
          port: 9999
          path: /actuator/health/readiness

configurations:
  datadog: true
  env:
    ## Application
    - key: APP_ENVIRONMENT
      value: "ta"
    - key: APP_LOG_LEVEL
      value: "DEBUG"
    - key: APP_NAME
      value: "pmt-processing-simulation-api"
    - key: APP_PLATFORM
      value: "aws"
    - key: BANK_ACCOUNT_CONNECTOR_RETRY_ATTEMPTS
      value: "3"
    - key: BANK_ACCOUNT_CONNECTOR_RETRY_BACKOFF
      value: "100"
    - key: BANK_ACCOUNT_CONNECTOR_URL
      value: "http://pmt-etransfer-bank-account-connector.6256-nonproduction-staging-ta.svc.cluster.local:8080"
    - key: DIRECT_PARTICIPANT_IDENTIFIER
      value: "CA000006"
    - key: DISABLE_SSL_VALIDATION
      value: "true"
    - key: LIMITS_VELOCITY_URL
      value: "https://pmt-ta.apis.bngf.local"
    - key: MONEY_REQUEST_URL
      value: "https://pmt-ta.apis.bngf.local"
    - key: PAYMENT_REGISTRATION_URL
      value: "https://pmt-ta.apis.bngf.local"
    - key: SPRING_PROFILE_INCLUDE
      value: "logging"
    - key: TENANT
      value: "BNC"

    ## Datadog
    - key: DD_SERVICE_NAME
      value: "pmt-processing-simulation-api"
    - key: DD_TRACE_ENABLED
      value: "true"
    - key: DD_LOGS_INJECTION
      value: "true"
    - key: DD_PROFILING_ENABLED
      value: "true"
    - key: DD_TRACE_DEBUG
      value: "false"
    - key: DD_SERVICE_MAPPING
      value: "jms:pmt-processing-simulation-api"
    - key: DD_TRACE_ANALYTICS_ENABLED
      value: "true"
    - key: DD_JMXFETCH_ENABLED
      value: "true"
    - key: DD_JMS_ANALYTICS_ENABLED
      value: "true"
    - key: DD_HTTP_SERVER_TAG_QUERY_STRING
      value: "true"
    - key: DD_HTTP_CLIENT_TAG_QUERY_STRING
      value: "true"
    - key: DD_TRACE_REPORT_HOSTNAME
      value: "true"
    - key: DD_ENV
      value: "app6256-payment-ta"
    - key: DD_TRACE_GLOBAL_TAGS
      value: "env:app6256-payment-ta"
    - key: DD_TRACE_SPAN_TAGS
      value: "service:pmt-processing-simulation-api"

  externalSecrets:
    - target:
        name: server-ssl-password
      data:
        - secretKey: SERVER_SSL_TRUSTSTOREPASSWORD
          remoteRef:
            key: applications/6256/non_production/staging/ta/nbc-root-truststore
            property: pwd
      source: secret-management.9b4eb9c6-dd35-4954-9c8f-b5ca71f60c91
      destination:
        type: env
    - target:
        name: server-truststore-certificate
      data:
        - secretKey: nbc-root-truststore.jks
          remoteRef:
            key: applications/6256/non_production/staging/ta/nbc-root-truststore
            property: jks
            decodingStrategy: Base64
      source: secret-management.9b4eb9c6-dd35-4954-9c8f-b5ca71f60c91
      destination:
        type: files
        path: /tmp/certs/truststore
    - target:
        name: okta-token-details
      data:
        - secretKey: JWKSAUTHORIZATIONSERVERID
          remoteRef:
            key: applications/6256/non_production/staging/ta/pmt-etransfer-payment-processing-simulation-api
            property: JWKSAUTHORIZATIONSERVERID
        - secretKey: JWKSURL
          remoteRef:
            key: applications/6256/non_production/staging/ta/pmt-etransfer-payment-processing-simulation-api
            property: JWKSURL
        - secretKey: OKTACLIENTID
          remoteRef:
            key: applications/6256/non_production/staging/ta/pmt-etransfer-payment-processing-simulation-api
            property: OKTACLIENTID
        - secretKey: OKTAPRIVATEKEY
          remoteRef:
            key: applications/6256/non_production/staging/ta/pmt-etransfer-payment-processing-simulation-api
            property: OKTAPRIVATEKEY
      source: secret-management.9b4eb9c6-dd35-4954-9c8f-b5ca71f60c91
      destination:
        type: env
