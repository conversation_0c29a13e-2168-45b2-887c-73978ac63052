# pmt-processing-simulation-api

This service simulates the processing of incoming payment requests. 
It validates the requests and calls the simulation endpoints of Registration API and Limits Velocities API.

### Links:

Documentation: [Confluence](https://wiki.bnc.ca/x/i87efg)

Contract:  [Confluence](https://wiki.bnc.ca/x/BQWYaw)

Code quality and tests: [SonarQube](https://sonar.bnc.ca/dashboard?id=APP7873.ca.bnc.payment%3Apmt-etransfer-payment-processing-simulation-api)

## Requirements

For building and running the application you need:

- [JDK 17](https://www.oracle.com/java/technologies/javase/jdk17-archive-downloads.html)
- [Maven 3](https://maven.apache.org)


## How to

1. Local application profile in service/src/main/resources/application-local.yml

2. Build the application:

```shell
./mvnw -s settings.xml clean install -U
```
3. Build the application and perform Sonar and mutation analysis:
```
./mvnw -s settings.xml -U clean install org.pitest:pitest-maven:mutationCoverage -DtimeoutConstant=8000 -Pmutation sonar:sonar -Dsonar.branch.name=local_<USER>>
```
4. Updates
   You can execute the following commands to see which dependencies are outdated, as well as the most recent versions available:
```
./mvnw -s settings.xml -U versions:display-parent-updates
./mvnw -s settings.xml -U versions:display-property-updates
```
The following command will actually update the pom files for you with the most recent versions:
```
./mvnw -s settings.xml -U versions:update-properties
```

### Running locally (in IDEA)

Use the `local` profile.

```shell
./mvnw -Dspring.profiles.active=local spring-boot:run
```

## Copyright

National Bank of Canada