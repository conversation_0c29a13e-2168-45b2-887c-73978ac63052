openapi: 3.0.3
info:
  version: 0.0.1
  description: An API that allows to retrieve or decline an incoming money request.
  title: Domestic Etransfer Incoming Money Request API
  termsOfService: http://www.bnc.ca/
  contact:
    name: Support PAYPRO
    email: <EMAIL>
    url: https://www.bnc.ca
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html
  x-api-id: 9761ae7b-2360-4275-a4e2-6ec4427dea73
  x-app-id: 6256
  x-audience:
    - internal
servers:
  - url: https://pmt.apis.bnc.ca/
    description: Production Environment
    x-stage-id: prod
    x-environment: production
  - url: https://pmt-tu.apis.bngf.local/
    description: TU Environment
    x-stage-id: tu
    x-environment: non_production
  - url: https://pmt-ti.apis.bngf.local/
    description: TI Environment
    x-stage-id: ti
    x-environment: non_production
  - url: https://pmt-ta.apis.bngf.local/
    description: TA Environment
    x-stage-id: ta
    x-environment: non_production
  - url: https://pmt-pp.apis.bngf.local/
    description: PP Environment
    x-stage-id: pp
    x-environment: non_production
tags:
  - name: IncomingMoneyRequest
    description: Etransfer Incoming Money Request API
paths:
  /et_pmt_proc/incoming-money-request/{interacMoneyRequestId}:
    parameters:
      - $ref: '#/components/parameters/InteracMoneyRequestId'
      - $ref: '#/components/parameters/ChannelId'
      - $ref: '#/components/parameters/ChannelType'
      - $ref: '#/components/parameters/RequestId'
      - $ref: '#/components/parameters/ClientId'
      - $ref: '#/components/parameters/Accept'
      - $ref: "#/components/parameters/Traceparent"
      - $ref: "#/components/parameters/Tracestate"
      - $ref: "#/components/parameters/Bncbusinesstraceid"
      - $ref: "#/components/parameters/ClientAgentId"
      - $ref: "#/components/parameters/AgentId"
    get:
      tags:
        - IncomingMoneyRequest
      summary: Get incoming money request.
      description: >-
        This service allows a participant to get domestic incoming money request
        information.
      operationId: getIncomingMoneyRequest
      responses:
        '200':
          $ref: '#/components/responses/200-success'
        '400':
          $ref: '#/components/responses/400-bad-request'
        '500':
          $ref: '#/components/responses/500-internal-server-error'
  /et_pmt_proc/incoming-money-request/{interacMoneyRequestId}/decline:
    parameters:
      - $ref: '#/components/parameters/InteracMoneyRequestId'
      - $ref: '#/components/parameters/ChannelId'
      - $ref: '#/components/parameters/ChannelType'
      - $ref: '#/components/parameters/ClientId'
      - $ref: '#/components/parameters/Accept'
      - $ref: '#/components/parameters/RequestId'
      - $ref: "#/components/parameters/Traceparent"
      - $ref: "#/components/parameters/Tracestate"
      - $ref: "#/components/parameters/Bncbusinesstraceid"
      - $ref: "#/components/parameters/ClientAgentId"
      - $ref: "#/components/parameters/AgentId"
    post:
      tags:
        - DomesticIncomingMoneyRequest
      summary: Decline an incoming money request
      description: This service allows a recipient to decline an incoming money request.
      operationId: decline
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeclineRequest'
      responses:
        '204':
          $ref: '#/components/responses/204-no-content'
        '400':
          $ref: '#/components/responses/400-bad-request'
        '404':
          $ref: '#/components/responses/404-not-found'
        '500':
          $ref: '#/components/responses/500-internal-server-error'
components:
  parameters:
    ChannelId:
      in: header
      name: x-channel-id
      description: The application source code.
      schema:
        type: string
        minLength: 4
        maxLength: 10
      required: true
      example: 5156
    ChannelType:
      in: header
      name: x-channel-type
      description: Indicates if channel is web or mobile.
      schema:
        $ref: '#/components/schemas/ChannelType'
      required: true
      example: WEB
    ClientId:
      in: header
      name: x-client-id
      description: The BNC id of the client.
      schema:
        type: string
        minLength: 1
        maxLength: 128
      required: true
      example: 30BD2E5746C6013778F4CD84D4533FAE6CF80C8C6F52C686691562F452D5FAZ0
    InteracMoneyRequestId:
      name: interacMoneyRequestId
      in: path
      required: true
      description: The Interac-generated money request reference number.
      schema:
        minLength: 8
        maxLength: 35
        type: string
      example: CAq7Q5ww
    Accept:
      name: Accept
      in: header
      description: "The Accept header is used to indicate the API REST version to use"
      required: true
      schema:
        type: string
        enum:
          - application/vnd.ca.bnc.pmt+json;version=v1
        example: application/vnd.ca.bnc.pmt+json;version=v1
    RequestId:
      in: header
      name: x-request-id
      description: >-
        Unique ID generated for each request used for message tracking purposes.
        Technical and unique traceability identifier. Used by monitoring and log
        tolls such as Datadog and Splunk.
      schema:
        type: string
        format: uuid
      required: true
      example: 3bcdf7fa-7e48-4565-9751-d8acbdb64d8b
    Traceparent:
      name: traceparent
      in: header
      description: |
        HTTP header containing information about the incoming request in a distributed tracing system.
        The traceparent header uses the version-trace_id-parent_id-trace_flags format where:
        - version is always 00.
        - trace_id is a hex-encoded trace id.
        - span_id is a hex-encoded span id.
        - trace_flags is a hex-encoded 8-bit field that contains tracing flags such as sampling, trace level, etc.
      required: true
      schema:
        type: string
        minLength: 1
        maxLength: 55
        example: "00-80e1afed08e019fc1110464cfa66635c-7a085853722dc6d2-01"
    Tracestate:
      name: tracestate
      in: header
      description: a comma-delimited list of key-value pairs, defined by section 3.3.
      schema:
        type: string
    Bncbusinesstraceid:
      name: bncbusinesstraceid
      in: header
      required: true
      description:
        HTTP header is to provide additional vendor-specific trace business identification information across different distributed systems.
        This reference contains a Universally Unique IDentifier (UUID) compliant with version 4 of standard RFC4122.
      schema:
        type: string
        format: uuid
        minLength: 36
        maxLength: 36
    ClientAgentId:
      in: header
      name: x-client-agent-id
      description: The bncId or the OS id of the authenticated employee performing an action on behalf of his employer, who is a commercial client of the bank.
      schema:
        $ref: '#/components/schemas/ClientAgentId'
    AgentId:
      in: header
      name: x-agent-id
      description: The shortid of the agent that initiate the payment for on behalf
      schema:
        $ref: '#/components/schemas/AgentId'
  responses:
    200-success:
      description: Success
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DomesticFulfillmentMoneyRequest'
    204-no-content:
      description: OK - No Content
    400-bad-request:
      description: Bad Request - Validation Errors
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Errors'
    404-not-found:
      description: Resources Not Found
    500-internal-server-error:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Errors'
  schemas:
    CreditTransferTransaction39Decline:
      description: >-
        Set of elements providing information specific to the individual credit
        transfer(s).
      type: object
      required:
        - paymentIdentification
      properties:
        paymentIdentification:
          $ref: '#/components/schemas/PaymentIdentification7'
        supplementaryData:
          $ref: '#/components/schemas/DeclineSupplementaryData'
    DeclineRequest:
      type: object
      required:
        - FIToFICustomerCreditTransferV08
      properties:
        FIToFICustomerCreditTransferV08:
          $ref: '#/components/schemas/FIToFICustomerCreditTransferV08Decline'
    Errors:
      type: object
      description: Returns a list of errors.
      properties:
        errors:
          type: array
          items:
            $ref: '#/components/schemas/Error'
    Error:
      description: Error code.
      type: object
      title: Error
      required:
        - code
        - text
        - origin
      properties:
        code:
          description: The normalized error code.
          title: Code
          type: string
          maxLength: 50
          example: ETRANSFER_NOT_EXIST
        text:
          description: Normalized description of the error code in case of an error.
          title: Text
          type: string
          maxLength: 2000
          example: Error description
        origin:
          description: The micro-service where the error occurred.
          title: Origin
          type: string
          maxLength: 55
          example: pmt-domestic-interac-etransfer-api
        rule:
          description: The business rule where the error occurred.
          title: Rule
          type: string
          maxLength: 50
          example: R_IEC_AUT_001
    FIToFICustomerCreditTransferV08Decline:
      type: object
      required:
        - groupHeader
        - creditTransferTransactionInformation
      properties:
        groupHeader:
          $ref: '#/components/schemas/GroupHeader93'
        creditTransferTransactionInformation:
          $ref: '#/components/schemas/CreditTransferTransaction39Decline'
    GroupHeader93:
      description: >-
        Set of characteristics shared by all individual transactions included in
        the message.
      type: object
      properties:
        messageIdentification:
          description: >-
            Point to point reference, as assigned by the instructing party, and
            sent to the next party in the chain to unambiguously identify the
            message.<br/><br/> Usage: The instructing party has to make sure
            that MessageIdentification is unique per instructed party for a
            pre-agreed period.<br/><br/> Same as x-correlation-id.
          type: string
          minLength: 1
          maxLength: 35
          example: d04273e9-0146-45c2-b12e-3ef18ef8589c
        creationDateTime:
          description: Date and time at which the message was created.
          type: string
          format: date-time
          example: '2019-05-05T17:29:12.123000Z'
      required:
        - messageIdentification
        - creationDateTime
    PaymentIdentification7:
      type: object
      required:
        - endToEndIdentification
      properties:
        instructionIdentification:
          description: >-
            The unique business transaction ID.<br/></br> Consumer generated
            transaction identifier.
          type: string
          minLength: 1
          maxLength: 35
        endToEndIdentification:
          description: >-
            EndToEndIdentification is a transaction identifier used across BNC
            systems (end-to-end chain). It is internal to BNC (does not
            represent Interac Payment Reference Number that is used as a
            parameter in the URI).
          type: string
          minLength: 1
          maxLength: 35
    DeclineSupplementaryData:
      description: >-
        Additional information that cannot be captured in the structured
        elements and/or any other specific block.
      type: object
      properties:
        declineReason:
          description: Message to requester.
          type: string
          minLength: 0
          maxLength: 400
    DomesticFulfillmentMoneyRequest:
      description: Message data block.
      type: object
      required:
        - CreditorPaymentActivationRequest
      properties:
        creditorPaymentActivationRequest:
          $ref: '#/components/schemas/CreditorPaymentActivationRequest'
    CreditorPaymentActivationRequest:
      description: Message data block.
      type: object
      required:
        - groupHeader
        - paymentInformation
      properties:
        groupHeader:
          $ref: '#/components/schemas/GroupHeader'
        paymentInformation:
          $ref: '#/components/schemas/PaymentInformation'
    GroupHeader:
      description: Group header data block wrapper element
      type: object
      required:
        - messageIdentification
        - creationDateTime
        - numberOfTransactions
        - initiatingParty
      properties:
        messageIdentification:
          description: Unique reference identification for this message
          type: string
          minLength: 1
          maxLength: 35
          example: d04273e9-0146-45c2-b12e-3ef18ef8589c
        creationDateTime:
          description: Date and time at which the message was created.
          type: string
          format: date-time
          example: '2019-05-05T17:29:12.123000Z'
        numberOfTransactions:
          description: >-
            Number of individual transactions contained in the message. Expected
            value is '1'.
          type: integer
          example: 1
        initiatingParty:
          $ref: '#/components/schemas/InitiatingParty'
    InitiatingParty:
      description: >-
        Party that initiates the transaction(s) on behalf of the creditor. Use
        this only if different than creditor or ultimate creditor.
      type: object
      required:
        - name
      properties:
        name:
          description: >-
            Name of the initiating party. If there is no Initiating party  then
            the value 'NOTPROVIDED' can be used in this name field.
          type: string
          minLength: 1
          maxLength: 140
          example: John Smith
    PaymentInformation:
      description: >-
        Payment information block containing the details of this payment
        request.
      type: object
      required:
        - expiryDate
        - moneyRequestStatus
        - paymentCondition
        - creditTransferTransaction
      properties:
        categoryPurpose:
          $ref: '#/components/schemas/CategoryPurpose'
        expiryDate:
          description: >
            Requested expiry date for this payment request.  Date and time by
            which the debtor must accept or reject the request. Beyond this
            date, the request becomes void and cannot be processed anymore. UTC
            format YYYY-MM-DDThh:mm:ss:sssZ.
          type: string
          format: date-time
          example: '2019-05-05T17:29:12.123000Z'
        moneyRequestStatus:
          $ref: '#/components/schemas/MoneyRequestStatus'
        paymentCondition:
          $ref: '#/components/schemas/PaymentCondition'
        debtor:
          $ref: '#/components/schemas/Debtor'
        creditTransferTransaction:
          $ref: '#/components/schemas/CreditTransferTransaction'
    CategoryPurpose:
      description: Wrapper element for the payment category purpose information.
      type: object
      properties:
        code:
          description: |
            Identifies the payment category purpose:
              * ANN - Annuity
              * INV - Investment
              * INS - Insurance
              * MTG - Mortgage
              * RLS - Rent/Leases
              * BPY - Bill Payment
              * AP  - Accounts Payable
              * DON - Donations
              * EXP - Expense Payment
              * CCB - Canada Child Benefit
              * OAS - Old Age Security
              * EI  - Employment Insurance
          enum:
            - ANN
            - INV
            - INS
            - MTG
            - RLS
            - BPY
            - AP
            - DON
            - EXP
            - CCB
            - OAS
            - EI
          type: string
          example: INS
    MoneyRequestStatus:
      description: The status of the request for payment.
      enum:
        - INITIATED
        - AVAILABLE_TO_BE_FULFILLED
        - FULFILLED
        - DECLINED
        - CANCELLED
        - EXPIRED
        - DEPOSIT_FAILED
        - DEPOSIT_COMPLETE
      type: string
      example: AVAILABLE_TO_BE_FULFILLED
    PaymentCondition:
      description: Conditions for the execution of the payment.
      required:
        - amountModificationAllowed
      type: object
      properties:
        amountModificationAllowed:
          description: >-
            Flag indicating if the request for payment can be fulfilled by a
            paying with a different amount than the requested amount.
          type: boolean
          example: false
    Debtor:
      description: This identifies the Debtor (for this request for payment).
      type: object
      properties:
        name:
          description: Debtor name
          type: string
          minLength: 1
          maxLength: 140
          example: John Smith
    CreditTransferTransaction:
      description: >-
        This block contains specific information about this request such as
        amount and reference ids. Restriction only one single occurrence of this
        data block is allowed.
      required:
        - amount
        - creditorAgent
        - payment_identification
      type: object
      properties:
        payment_identification:  # PmtId
          $ref: '#/components/schemas/PaymentIdentification6'
        amount:
          $ref: '#/components/schemas/Amount'
        creditorAgent:
          $ref: '#/components/schemas/CreditorAgent'
        creditor:
          $ref: '#/components/schemas/Creditor'
        remittanceInformation:
          $ref: '#/components/schemas/RemittanceInformation'
        invoice:
          $ref: '#/components/schemas/Invoice'
    Amount:
      description: The requested amount and currency.
      required:
        - instructedAmount
        - currency
      type: object
      properties:
        instructedAmount:
          description: The Requested amount. The maximum decimal digits allowed are two.
          type: number
          format: decimal
          minimum: 0
          exclusiveMinimum: true
          multipleOf: 0.01
          example: 10.52
        currency:
          description: >-
            Requested Payment's Currency code. The currency code must conform
            with ISO 4217.
          type: string
          minLength: 1
          maxLength: 3
          example: CAD
    CreditorAgent:
      description: Creditor FI details.
      required:
        - financialInstitutionIdentification
      type: object
      properties:
        financialInstitutionIdentification:
          $ref: '#/components/schemas/FinancialInstitutionIdentification'
    FinancialInstitutionIdentification:
      description: Financial institution identification information.
      required:
        - clearingSystemMemberIdentification
      type: object
      properties:
        clearingSystemMemberIdentification:
          $ref: '#/components/schemas/ClearingSystemMemberIdentification'
    ClearingSystemMemberIdentification:
      description: Clearing system member identification information.
      required:
        - memberIdentification
      type: object
      properties:
        memberIdentification:
          description: >-
            Creditor FI member id (FI participant id). Also known as 'Requestor
            participant ID'.
          type: string
          minLength: 1
          maxLength: 35
          example: steOqBV2eWGVDMWJuPH2yegbC3lyEaABL9u
    Creditor:
      description: Information about the creditor that made this request.
      type: object
      properties:
        name:
          description: Creditor name (Requestor name)
          type: string
          minLength: 1
          maxLength: 140
          example: John Smith
        contactDetails:
          $ref: '#/components/schemas/ContactDetails'
    ContactDetails:
      description: Additional contact details for the creditor.
      type: object
      properties:
        mobileNumber:
          description: Creditor mobile phone number.
          type: string
          pattern: \+[0-9]{1,3}-[0-9()+\-]{1,30}
          format: phone
          minLength: 1
          maxLength: 30
          example: ******-555-1212
        emailAddress:
          description: Creditor email address.
          type: string
          format: email
          minLength: 1
          maxLength: 256
          example: <EMAIL>
    RemittanceInformation:
      type: object
      properties:
        unstructured:
          type: array
          items:
            type: string
            minLength: 1
            maxLength: 140
          description: >-
            Remittance information in an unstructured form. Up to 3 elements
            allowed for a total of 420 characters.
          maxItems: 3
          x-code-size: 2001
          example:
            - 700 De La Gauchetiere O
            - 'Montreal, QC'
            - H3B 4L5
        structured:
          type: array
          items:
            $ref: '#/components/schemas/StructuredRemittanceInformation16'
          description: >-
            Remittance information in a structured form. Up to 5 block allowed.
            This can be used to accommodate up to 5 individual remittance documents/invoices in a message.
          maxItems: 5
    StructuredRemittanceInformation16:
      type: object
      properties:
        referredDocumentInformation:
          $ref: '#/components/schemas/ReferredDocumentInformation7'
        referredDocumentAmount:
          $ref: '#/components/schemas/RemittanceAmount2'
        creditorReferenceInformation:
          $ref: '#/components/schemas/CreditorReferenceInformation2'
        invoicer:
          $ref: '#/components/schemas/PartyIdentification135'
        invoicee:
          $ref: '#/components/schemas/PartyIdentification135'
        additionalRemittanceInformation:
          type: string
          description: This element is used to provide additional information, in free text form. (e.g. invoice description. etc.)
          minLength: 1
          maxLength: 140
    ReferredDocumentInformation7:
      type: object
      description: Wrapper element for Identification and content of referred document
      properties:
        type:
          $ref: '#/components/schemas/ReferredDocumentType4'
        number:
          description: Unique and unambiguous identification number of referred document
          type: string
          minLength: 1
          maxLength: 35
        relatedDate:
          description: Date associated with referred document. Format is YYYY-MM-DD
          type: string
          format: date
          example: '2019-05-05'
    ReferredDocumentType4:
      type: object
      description: Wrapper element for the type of referred document
      required:
        - codeOrProprietary
      properties:
        codeOrProprietary:
          $ref: '#/components/schemas/ReferredDocumentType3Choice'
    ReferredDocumentType3Choice:
      type: object
      description: Wrapper element for the type details of referred document
      required:
        - code
      properties:
        code:
          description: |
            Document type based on the following codes:
              * MSIN - meaning MeteredServiceInvoice
              * CNFA - meaning CreditNoteRelatedToFinancialAdjustment
              * DNFA - meaning DebitNoteRelatedToFinancialAdjustment
              * CINV - meaning CommercialInvoice
              * CREN - meaning CreditNote
              * DEBN - meaning DebitNote
              * HIRI - meaning HireInvoice
              * SBIN - meaning SelfBilledInvoice
              * CMCN - meaning CommercialContract
              * SOAC - meaning StatementOfAccount
              * DISP - meaning DispatchAdvice
              * BOLD - meaning BillOfLading
              * VCHR - meaning Voucher
              * AROI - meaning AccountReceivableOpenItem
              * TSUT - meaning TradeServicesUtilityTransaction
              * PUOR - meaning PurchaseOrder
          type: string
          enum:
            - MSIN
            - CNFA
            - DNFA
            - CINV
            - CREN
            - DEBN
            - HIRI
            - SBIN
            - CMCN
            - SOAC
            - DISP
            - BOLD
            - VCHR
            - AROI
            - TSUT
            - PUOR
          example: 'MSIN'
    RemittanceAmount2:
      type: object
      description: Wrapper element for details on the amounts of referred document
      properties:
        duePayableAmount:
          type: number
          description: Amount due and payable to the creditor
          format: decimal
          multipleOf: 0.00001
        adjustmentAmountAndReason:
          $ref: '#/components/schemas/DocumentAdjustment1'
        remittedAmount:
          type: number
          description: Amount paid
          format: decimal
          multipleOf: 0.00001
    DocumentAdjustment1:
      type: object
      description: Wrapper element for detailed information on the adjusted amount and reason of the adjustment
      required:
        - amount
      properties:
        amount:
          type: number
          description: Adjustment amount
          format: decimal
          multipleOf: 0.00001
        creditDebitIndicator:
          type: string
          description: >-
            Indicates whether the adjustment must be subtracted or added to the total amount.
            Accepted values are CRDT or DBIT
          enum:
            - CRDT
            - DBIT
        reason:
          type: string
          description: Reason for adjustment.
          minLength: 1
          maxLength: 4
        additionalInformation:
          type: string
          description: Further information and details on the adjustment
          minLength: 1
          maxLength: 140
    CreditorReferenceInformation2:
      type: object
      description: Wrapper element for additional reference information provided by the Creditor (e.g. such as purchase order, etc)
      properties:
        type:
          $ref: '#/components/schemas/CreditorReferenceType2'
        reference:
          type: string
          description: This element contains reference info provided by the Creditor (e.g. the purchase order)
          minLength: 1
          maxLength: 35
    CreditorReferenceType2:
      type: object
      description: Wrapper element for the type of reference document
      required:
        - codeOrProprietary
      properties:
        codeOrProprietary:
          $ref: '#/components/schemas/CreditorReferenceType1Choice'
    CreditorReferenceType1Choice:
      type: object
      description: Wrapper element for the type details of referred document
      required:
        - code
      properties:
        code:
          description: |
            Type of creditor reference in coded form. Valid values:
              * RADM - meaning RemittanceAdviceMessage
              * RPIN - meaning RelatedPaymentInstruction
              * FXDR - meaning ForeignExchangeDealReference
              * DISP - meaning DispatchAdvice
              * PUOR - meaning PurchaseOrder
              * SCOR - meaning StructuredCommunicationReference
          type: string
          enum:
            - RADM
            - RPIN
            - FXDR
            - DISP
            - PUOR
            - SCOR
          example: 'RADM'
    PartyIdentification135:
      type: object
      description: Wrapper element for Invoicer/Creditor information
      properties:
        name:
          type: string
          description: Name of the invoicer/invoicee. To be used when invoicer is different from Creditor or Ultimate Creditor.
          minLength: 1
          maxLength: 140
          example: 'John Doe'
        postalAddress:
          $ref: '#/components/schemas/PostalAddress24'
        identification:
          $ref: '#/components/schemas/Party38Choice'
        contactDetails:
          $ref: '#/components/schemas/Contact4'
    PostalAddress24:
      type: object
      description: Postal address details
      properties:
        addressType:
          $ref: '#/components/schemas/AddressType3Choice'
        department:
          type: string
          description: Identification of a division of a large organisation or building
          minLength: 1
          maxLength: 70
        subDepartment:
          type: string
          description: Identification of a sub-division of a large organisation or building
          minLength: 1
          maxLength: 70
        streetName:
          type: string
          description: Name of a street or thoroughfare
          minLength: 1
          maxLength: 70
          example: 'Rue de la Gauchetiere O'
        buildingNumber:
          type: string
          description: Number that identifies the position of a building on a street
          minLength: 1
          maxLength: 16
          example: '700'
        postCode:
          type: string
          description: Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail
          minLength: 1
          maxLength: 16
          example: 'H3B 4L5'
        townName:
          type: string
          description: Name of a built-up area, with defined boundaries, and a local government
          minLength: 1
          maxLength: 35
          example: 'Montréal'
        countrySubDivision:
          type: string
          description: Identifies a subdivision of a country such as state, region, county
          minLength: 1
          maxLength: 35
        country:
          type: string
          description: Nation with its own government (based om the ISO 3166, Alpha-2 code)
          enum:
            - AA
            - AB
            - AD
            - AE
            - AF
            - AG
            - AI
            - AL
            - AM
            - AN
            - AO
            - AQ
            - AR
            - AS
            - AT
            - AU
            - AW
            - AX
            - AZ
            - BA
            - BB
            - BD
            - BE
            - BF
            - BG
            - BH
            - BI
            - BJ
            - BL
            - BM
            - BN
            - BO
            - BQ
            - BR
            - BS
            - BT
            - BV
            - BW
            - BY
            - BZ
            - C2
            - CA
            - CC
            - CD
            - CF
            - CG
            - CH
            - CI
            - CK
            - CL
            - CM
            - CN
            - CO
            - CR
            - CU
            - CV
            - CW
            - CX
            - CY
            - CZ
            - DE
            - DJ
            - DK
            - DM
            - DO
            - DZ
            - EC
            - EE
            - EG
            - EH
            - ER
            - ES
            - ET
            - FI
            - FJ
            - FK
            - FM
            - FO
            - FR
            - GA
            - GB
            - GD
            - GE
            - GF
            - GG
            - GH
            - GI
            - GL
            - GM
            - GN
            - GP
            - GQ
            - GR
            - GS
            - GT
            - GU
            - GW
            - GY
            - HK
            - HM
            - HN
            - HR
            - HT
            - HU
            - ID
            - IE
            - IL
            - IM
            - IN
            - IO
            - IQ
            - IR
            - IS
            - IT
            - JE
            - JM
            - JO
            - JP
            - K1
            - KE
            - KG
            - KH
            - KI
            - KM
            - KN
            - KP
            - KR
            - KW
            - KY
            - KZ
            - LA
            - LB
            - LC
            - LI
            - LK
            - LR
            - LS
            - LT
            - LU
            - LV
            - LY
            - MA
            - MC
            - MD
            - ME
            - MF
            - MG
            - MH
            - MK
            - ML
            - MM
            - MN
            - MO
            - MP
            - MQ
            - MR
            - MS
            - MT
            - MU
            - MV
            - MW
            - MX
            - MY
            - MZ
            - NA
            - NC
            - NE
            - NF
            - NG
            - NI
            - NL
            - NO
            - NP
            - NR
            - NU
            - NZ
            - OM
            - PA
            - PE
            - PF
            - PG
            - PH
            - PK
            - PL
            - PM
            - PN
            - PR
            - PS
            - PT
            - PW
            - PY
            - QA
            - QM
            - QN
            - QO
            - QP
            - QQ
            - QR
            - QS
            - QT
            - QU
            - QV
            - QW
            - QX
            - QY
            - QZ
            - RE
            - RO
            - RS
            - RU
            - RW
            - S1
            - SA
            - SB
            - SC
            - SD
            - SE
            - SG
            - SH
            - SI
            - SJ
            - SK
            - SL
            - SM
            - SN
            - SO
            - SR
            - SS
            - ST
            - SV
            - SX
            - SY
            - SZ
            - TC
            - TD
            - TF
            - TG
            - TH
            - TJ
            - TK
            - TL
            - TM
            - TN
            - TO
            - TP
            - TR
            - TT
            - TV
            - TW
            - TZ
            - UA
            - UG
            - UM
            - US
            - UY
            - UZ
            - VA
            - VC
            - VE
            - VG
            - VI
            - VN
            - VU
            - WF
            - WS
            - XA
            - XB
            - XD
            - XE
            - XF
            - XG
            - XN
            - XP
            - XQ
            - XR
            - XS
            - XT
            - XU
            - XV
            - XW
            - XY
            - XZ
            - YE
            - YT
            - YU
            - ZA
            - ZM
            - ZW
          example: 'CA'
        addressLine:
          type: array
          description: Information that locates and identifies a specific address, as defined by postal services, presented in free format text
          items:
            type: string
            minLength: 1
            maxLength: 70
          example:
            - 700 De La Gauchetiere O
            - Montreal, QC
            - H3B 4L5
          maxItems: 7
    AddressType3Choice:
      type: object
      description: Identifies the nature of the postal address
      required:
        - code
      properties:
        code:
          description: |
            Type of address expressed as a code: Valid values:
              * ADDR - meaning Postal Address
              * PBOX - meaning POBox Address
              * HOME - meaning Residential Address
              * BIZZ - meaning Business Address
              * MLTO - meaning MailTo Address
              * DLVY - meaning DeliveryTo Address
          type: string
          enum:
            - ADDR
            - PBOX
            - HOME
            - BIZZ
            - MLTO
            - DLVY
          example: 'ADDR'
    Party38Choice:
      type: object
      description: Wrapper element for Invoicer identification
      properties:
        organisationIdentification:
          $ref: '#/components/schemas/OrganisationIdentification29'
      required:
        - organisationIdentification
    OrganisationIdentification29:
      type: object
      description: Wrapper element
      properties:
        other:
          $ref: '#/components/schemas/GenericOrganisationIdentification1'
      required:
        - other
    GenericOrganisationIdentification1:
      type: object
      description: Wrapper element
      properties:
        identification:
          type: string
          description: Identification number or value
          minLength: 1
          maxLength: 35
        schemeName:
          $ref: '#/components/schemas/OrganisationIdentificationSchemeName1Choice'
      required:
        - identification
    OrganisationIdentificationSchemeName1Choice:
      type: object
      description: Wrapper element to define the scheme used for the identification
      properties:
        code:
          type: string
          description: |
            Name of the identification scheme, in a coded form as published by ISO 20022 their ExternalCodeSets list. Valid values:
              * BANK - meaning BankPartyIdentification
              * CBID - meaning Central Bank Identification Number
              * CHID - meaning Clearing Identification Number
              * CINC - meaning CertificateOfIncorporationNumber
              * COID - meaning CountryIdentificationCode
              * CUST - meaning CustomerNumber
              * DUNS - meaning Data Universal Numbering System
              * EMPL - meaning EmployerIdentificationNumber
              * GS1G - meaning GS1GLNIdentifier
              * SREN - meaning SIREN
              * SRET - meaning SIRET
              * TXID - meaning TaxIdentificationNumber
          enum:
            - BANK
            - CBID
            - CHID
            - CINC
            - COID
            - CUST
            - DUNS
            - EMPL
            - GS1G
            - SREN
            - SRET
            - TXID
          example: 'BANK'
      required:
        - code
    Contact4:
      type: object
      description: Set of elements used to indicate how to contact the invoicer/invoicee
      properties:
        name:
          type: string
          description: Invoicer/Invoicee contact name
          minLength: 1
          maxLength: 140
          example: 'John Doe'
        phoneNumber:
          type: string
          description: Invoicer/Invoicee contact phone number
          minLength: 1
          maxLength: 30
        mobileNumber:
          type: string
          description: Invoicer/Invoicee contact mobile number
          minLength: 1
          maxLength: 30
        faxNumber:
          type: string
          description: Invoicer/Invoicee contact fax number
          minLength: 1
          maxLength: 30
        emailAddress:
          type: string
          description: Invoicer/Invoicee contact email
          minLength: 1
          maxLength: 2048
          format: email
    Invoice:
      description: Document enclosed in the notification.
      required:
        - type
        - issueDate
      type: object
      properties:
        type:
          $ref: '#/components/schemas/Type'
        issueDate:
          $ref: '#/components/schemas/ISODateTime'
    Type:
      description: Invoice type information.
      required:
        - code
        - identification
      type: object
      properties:
        code:
          description: Document type
          type: string
          enum:
            - CINV
            - CNFA
            - CONT
            - CREN
            - DEBN
            - DISP
            - DNFA
            - HIRI
            - INVS
            - MSIN
            - PROF
            - PUOR
            - QUOT
            - SBIN
            - SPRR
            - TISH
            - USAR
          example: CINV
        identification:
          description: Identification of the document (InvoiceNumber)
          type: string
          minLength: 1
          maxLength: 35
          example: URJXQKU1PPG6P5DNAMDGXPNJWUAP6A7FHS7
    ISODateTime:
      type: string
      description: >-
        "A particular point in the progression of time defined and expressed in either
        UTC time format (YYYY-MM-DDThh:mm:ss.sssZ), local time with UTC offset format
        (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm), or local time format (YYYY-MM- DDThh:mm:ss.sss).
        These representations are defined in XML Schema Part 2: Datatypes Second Edition
        - W3C Recommendation 28 October 2004 which is aligned with ISO 8601. "
      format: date-time
      example: '2020-01-23T12:34:56.123Z'
    ClientAgentId:
      type: string
      minLength: 1
      maxLength: 64
      example : "30BD2E5746C6013778F4CD84D4533FAE6CF80C8C6F52C686691562F452D5FAZ0"
    AgentId:
      type: string
      pattern: '^[\w\p{L}\p{Mn}.,\-\/]*$'
      example: ourk020
    ChannelType:
      type: string
      description: >-
        The application source type. Indicates if the channel is web or mobile.
      enum:
        - WEB
        - MOBILE
    PaymentIdentification6:
      type: object
      properties:
        instruction_identification:  # InstrId
          description: >-
            Creditor FI reference number for this payment request, as set up by Creditor's
            FI.
          type: string
          minLength: 1
          maxLength: 35
        end_to_end_identification: # EndToEndId
          description: >-
            Creditor/Originating Party payment request reference number as set up
            by the Creditor/Originating Party.
            If a reference is not provided by the Creditor then this element should
            be set to 'NOTPROVIDED'
          type: string
          minLength: 1
          maxLength: 35
      required:
        - instruction_identification
        - end_to_end_identification