target/
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders
.checkstyle
mqjms*

/service/src/main/resources/truststore.jks
/service/src/main/resources/truststore_amazon.jks
/service/src/main/resources/keystore.jks
/sanity-test/src/test/resources/truststore.jks
/sanity-test/src/test/resources/keystore.jks

**/target

# IntelliJ project files
.idea/workspace.xml
.idea/*
.idea
*.iml
out
gen

# Eclipse Core
.project

# External tool builders
.externalToolBuilders/

# PyDev specific (Python IDE for Eclipse)
*.pydevproject

# CDT-specific (C/C++ Development Tooling)
.cproject

# JDT-specific (Eclipse Java Development Tools)
.classpath

# Java annotation processor (APT)
.factorypath

# PDT-specific (PHP Development Tools)
.buildpath

# sbteclipse plugin
.target

# Tern plugin
.tern-project

# TeXlipse plugin
.texlipse

# STS (Spring Tool Suite)
.springBeans

# Code Recommenders
.recommenders/

**/target/
**/src/main/generated/

/config/copy_config_files.ps1

/*.iml
/jacoco.exec
component-test/jacoco.exec

.DS_Store
