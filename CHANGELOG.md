# Changelog

## APP6256 - Domain: Payment - Application: pmt-processing-simulation-api

**Description**:  Simulation of an eTransfer payment processing, in Synchronous mode.

All notable changes will be documented in this file following [Changelog](https://keepachangelog.com/en/1.0.0/) standard
and [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

```Added``` - for new features.\
```Changed``` - for changes in existing functionality.\
```Deprecated``` - for soon-to-be removed features.\
```Removed``` - for now removed features.\
```Fixed``` - for any bug fixes.\
```Security``` - in case of vulnerabilities.

### **CAUTION

** Access to Git, Jira and Confluence are required ([To make a request](https://jira.bnc.ca/servicedesk/customer/portal/1/create/1849))

Deployment plan (see the 'stages' sections for more information):

- [CI](https://git.bnc.ca/projects/APP7873/repos/shared-aws-ci-pipeline-squad12/browse/vars/sharedPipeline.groovy)
- [CD](https://git.bnc.ca/projects/APP7873/repos/shared-aws-cd-pipeline-squad12/browse/vars/sharedPipeline.groovy)

Code quality and tests: [SonarQube](https://sonar.bnc.ca/dashboard?id=APP7873.ca.bnc.payment%3Awalkingskeletonaws)

## [0.1.8] FRI SEP 19 2025

- https://jira.bnc.ca/browse/PPET-70401
- https://jira.bnc.ca/browse/PPET-70400
- https://jira.bnc.ca/browse/PPET-70399
- https://jira.bnc.ca/browse/PPET-70398
- https://jira.bnc.ca/browse/PPET-71505
- https://jira.bnc.ca/browse/PPET-71511
- https://jira.bnc.ca/browse/PPET-72151
- https://jira.bnc.ca/browse/PPET-75639
- https://jira.bnc.ca/browse/PPET-74249
- https://jira.bnc.ca/browse/PPET-76359
- https://jira.bnc.ca/browse/PPET-74954


#### ```Added```

- Bank account connector module
- @FeignClient Configuration for bank account connector
- Bank account connector mapping for simulation api
- Unhappy path process scenario CTD1 (timeout)
- Unhappy path process scenario CTD2 (Bad Request)
- Unhappy path process scenario CTD3 (Internal Server Error)
- Unhappy path process scenario CTD4 (Service unavailable)

```Changed```

- Make jobs in release workflow sequential
- Normalize TENANT environment variable
- Update security library to v2
- Component test refactored

```Fixed```

- Update dependencies to resolve snyk
- Use a newer version of a pipeline
- Activate use of local settings.xml

## [0.1.5] MON JUL 14 2025

- https://jira.bnc.ca/browse/PPET-64430
- https://jira.bnc.ca/browse/PPET-65350
- https://jira.bnc.ca/browse/PPET-65351
- https://jira.bnc.ca/browse/PPET-65353
- https://jira.bnc.ca/browse/PPET-65349

#### ```Fixed```

- Remove `Accept header` from simulation api contract

#### ```Added```

- Call to Money Request API

## [0.1.4] FRI JUN 20 2025

- https://jira.bnc.ca/browse/PPET-66887

#### ```Fixed```

- Client ID for Bruno Collection targeted TA environment

## [0.1.3] THU JUN 19 2025

- https://jira.bnc.ca/browse/PPET-66130
- https://jira.bnc.ca/browse/PPET-66887

#### ```Added```

- Bruno config for TA
- Config prooperty for payment registration api version

#### ```Fixed```

- Simulation api startup for local spring boot profile
- Http Bad request error returned by payment registration for our `Accept header`
- Http Internal Error returned for wrong EndToEndIdentification generated by Bruno script

## [0.1.1] MON JUN 16 2025

- https://jira.bnc.ca/browse/CD-28950
- https://jira.bnc.ca/browse/CD-28171
- https://jira.bnc.ca/browse/CD-28172
- https://jira.bnc.ca/browse/CD-28173
- https://jira.bnc.ca/browse/CD-28155
- https://jira.bnc.ca/browse/CD-29017
- https://jira.bnc.ca/browse/CD-29439
- https://jira.bnc.ca/browse/CD-29505
- https://jira.bnc.ca/browse/PPET-62115
- https://jira.bnc.ca/browse/PPET-66130

#### ```Added```

- implement Okta Step Definitions Code And Gherkins Sentences
- handle error when registration replies with 503
- validate if okta return http 400 CTA6
- validate if client is not registered to Interac CTA7
- add happy path process scenario CT12
- Add config for TA deployment targetting staging

#### ```Changed```

- Migrate service from 7873 to 6256
- deployment to non production
- Standardizing Exception Handling for Controller Advice

#### ```Fixed```

- duplicate some okta details config
- invalid scope for payment registration

## [[0.1.0]](https://jira.bnc.ca/browse/OSP-1654)

#### ```Added```

- Add Initial code
