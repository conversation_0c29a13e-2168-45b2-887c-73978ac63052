meta {
  name: Simulate domestic payment
  type: http
  seq: 1
}

put {
  url: {{URL}}/{{endToEndIdentification}}
  body: json
  auth: none
}

headers {
  Content-Type: application/vnd.ca.bnc.pmt+json
  x-channel-id: {{xChannelID}}
  x-channel-type: MOBILE
  X-Checkpoint-Key: {{checkpointKey}}
  x-client-id: {{xClientId}}
  x-request-id: {{xRequestId}}
  traceparent: 00-80e1afed08e019fc1110464cfa66635c-7a085853722dc6d2-01
  x-client-agent-id: 30BD2E5746C6013778F4CD84D4533FAE6CF80C8C6F52C686691562F452D5FAZ0
  x-agent-id: ourk020
  bncbusinesstraceid: 3bcdf7fa-7e48-4565-9751-d8acbdb64d8b
  tracestate: vendorname1=opaqueValue1
  Accept: application/vnd.ca.bnc.pmt+json;version=v1
}

body:json {
  {
    "FIToFICustomerCreditTransfer": {
      "groupHeader": {
        "messageIdentification": "{{messageIdentification}}",
        "creationDateTime": "{{currentTimestamp}}",
        "numberOfTransactions": "1"
      },
      "creditTransferTransactionInformation": {
        "paymentIdentification": {
          "instructionIdentification": "{{instructionIdentification}}",
          "endToEndIdentification": "{{endToEndIdentification}}"
        },
        "paymentTypeInformation": {
          "localInstrument": {
            "proprietary": "REGULAR_PAYMENT"
          }
        },
        "interbankSettlementAmount": {
          "amount": 1709.66,
          "currency": "CAD"
        },
        "interbankSettlementDate": "{{interbankSettlementDate}}",
        "debtor": {
          "name": "Agathe Thepower",
          "identification": {
            "organisationIdentification": {
              "other": [
                {
                  "identification": "CCCC9999"
                }
              ]
            }
          },
          "contactDetails": {
            "mobileNumber": "******-111-9999",
            "emailAddress": "<EMAIL>"
          }
        },
        "debtorAccount": {
          "identification": {
            "other": {
              "identification": "{{debtorAccountNumber}}"
            }
          }
        },
        "creditor": {
          "name": "Fonfec, Sophie",
          "contactDetails": {
            "mobileNumber": "******-555-1212"
          }
        },
        "creditorAccount": {
          "identification": {
            "other": {
              "identification": "{{creditorAccount}}"
            }
          }
        },
        "remittanceInformation": {
          "unstructured": [
            "weqetyueweqytwewuesadfdah",
            "asdftgfhsadgadashdggsdfas"
          ]
        },
        "mandateRelatedInformation": {
          "mandateIdentification": "***********",
          "frequencyTypePeriod": "MNTH",
          "countPerPeriod": 17,
          "supplementaryData": {
            "numberOfRemaining": 0,
            "currentOccurrence": 1,
            "originalChannelType": "WEB",
            "originalChannelId": "8131"
          }
        },
        "supplementaryData": {
          "creditorPreferredLanguage": "EN",
          "interacMoneyRequestId": "DDDD9999",
          "accountHolderName": "Agathe Thepower",
          "fraudSupplementaryInfo": {
            "clientIpAddress": "**************",
            "clientCardNumber": "****************",
            "clientDeviceFingerPrint": "ITM1234567899999",
            "clientAuthenticationMethod": "PASSWORD",
            "accountCreationDate": "2020-01-23"
          },
          "paymentAuthentication": {
            "securityQuestion": "b8f53795fcda7ff8fbaf3c3cc30d27a264d1c08",
            "securityAnswer": "c8d1c9487641cfc213b918f91c0917ab41be5d8b6360b352b2c05c94200093ec",
            "hashType": "SHA2",
            "hashSalt": "*************"
          },
          "clientType": "ORGANISATION",
          "creditorId": "95D2E46D-DC12-4510-BFEA-B5C268A206B2",
          "confirmationId": "1234567ZY"
        }
      }
    }
  }
}

script:pre-request {
  const moment = require("moment");
  const { v4: uuidv4 } = require('uuid');

  function generateRandomString(length) {
      var charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
          retVal = "";
      for (var i = 0, n = charset.length; i < length; ++i) {
          retVal += charset.charAt(Math.floor(Math.random() * n));
      }
      return retVal;
  }

  var uuidWithoutDash = uuidv4().replaceAll("-","");
  bru.setEnvVar("currentTimestamp", moment().toISOString());
  bru.setEnvVar("interbankSettlementDate", moment().format("YYYY-MM-DD"));
  bru.setEnvVar("messageIdentification", generateRandomString(35));
  bru.setEnvVar("instructionIdentification", generateRandomString(35));
  bru.setEnvVar("endToEndIdentification", uuidWithoutDash);
  bru.setVar('xRequestId', require("uuid").v4());
  
}
