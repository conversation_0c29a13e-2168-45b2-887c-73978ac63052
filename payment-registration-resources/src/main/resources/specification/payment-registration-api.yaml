openapi: 3.0.3
info:
  title: Payment Registration API Specification
  description: >-
    NBC client and partners registration end points for Payment Registration API.
  version: 1.0.0
  termsOfService: http://www.bnc.ca/
  contact:
    name: Support PAYPRO
    email: <EMAIL>
    url: https://www.bnc.ca
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html
  x-api-id: bedeb2b2-0549-4775-bd2e-a656b6631716
  x-app-id: 6255
  x-audience:
    - internal
servers:
  - url: https://pmt.apis.bnc.ca/
    description: Production Environment
    x-stage-id: prod
    x-environment: production
  - url: https://pmt-tu.apis.bngf.local/
    description: TU Environment
    x-stage-id: tu
    x-environment: non_production
  - url: https://pmt-ti.apis.bngf.local/
    description: TI Environment
    x-stage-id: ti
    x-environment: non_production
  - url: https://pmt-ta.apis.bngf.local/
    description: TA Environment
    x-stage-id: ta
    x-environment: non_production
  - url: https://pmt-pp.apis.bngf.local/
    description: PP Environment
    x-stage-id: pp
    x-environment: non_production

paths:
  /pmt_rail_net/registrations:
    post:
      tags:
        - Registration
      summary: Create Registration.
      description: >-
        This service can be used by a participant to allow their customer
        to Add Partner Registration
      operationId: addRegistration
      security:
        - oAuth2:
            - pmt:registration-management:create
      parameters:
        - $ref: '#/components/parameters/Traceparent'
        - $ref: '#/components/parameters/Tracestate'
        - $ref: '#/components/parameters/Bncbusinesstraceid'
        - $ref: '#/components/parameters/Accept'
        - $ref: '#/components/parameters/RequestId'
        - $ref: '#/components/parameters/ChannelId'
        - $ref: '#/components/parameters/ChannelType'
        - $ref: '#/components/parameters/ClientId'
        - $ref: '#/components/parameters/ClientAgentId'
        - $ref: '#/components/parameters/AgentId'
      requestBody:
        $ref: '#/components/requestBodies/ClientRegistrationRequest'
      responses:
        201:
          $ref: '#/components/responses/201-created'
        400:
          $ref: '#/components/responses/400-bad-request'
        500:
          $ref: '#/components/responses/500-internal-server-error'
    get:
      tags:
        - Registration
      summary: Retrieve client's list of registrations.
      operationId: getRegistrations
      description: >-
        This service allows a participant to retrieve the payment options available for creditor's handles.
      security:
        - oAuth2:
            - pmt:registration-management:read
      parameters:
        - $ref: '#/components/parameters/Traceparent'
        - $ref: '#/components/parameters/Tracestate'
        - $ref: '#/components/parameters/Bncbusinesstraceid'
        - $ref: '#/components/parameters/Accept'
        - $ref: '#/components/parameters/RequestId'
        - $ref: '#/components/parameters/ChannelId'
        - $ref: '#/components/parameters/ChannelType'
        - $ref: '#/components/parameters/ClientId'
        - $ref: '#/components/parameters/ClientAgentId'
        - $ref: '#/components/parameters/AgentId'
        - $ref: '#/components/parameters/RegistrationTypeFilter'
      responses:
        200:
          $ref: '#/components/responses/ClientRegistrationResponse'
        404:
          $ref: '#/components/responses/404-not-found'
        400:
          $ref: '#/components/responses/400-bad-request'
        500:
          $ref: '#/components/responses/500-internal-server-error'
  /pmt_rail_net/registrations/{registrationId}:
    put:
      tags:
        - Registration
      summary: Update Registration.
      operationId: updateRegistration
      description: >-
        This service allows a participant to update the registration available.
      security:
        - oAuth2:
            - pmt:registration-management:update
      parameters:
        - $ref: '#/components/parameters/registrationId'
        - $ref: '#/components/parameters/Traceparent'
        - $ref: '#/components/parameters/Tracestate'
        - $ref: '#/components/parameters/Bncbusinesstraceid'
        - $ref: '#/components/parameters/Accept'
        - $ref: '#/components/parameters/RequestId'
        - $ref: '#/components/parameters/ChannelId'
        - $ref: '#/components/parameters/ChannelType'
        - $ref: '#/components/parameters/ClientId'
        - $ref: '#/components/parameters/ClientAgentId'
        - $ref: '#/components/parameters/AgentId'

      requestBody:
        $ref: '#/components/requestBodies/ClientRegistrationRequest'
      responses:
        204:
          $ref: '#/components/responses/204-no-content'
        400:
          $ref: '#/components/responses/400-bad-request'
        404:
          $ref: '#/components/responses/404-not-found'
        500:
          $ref: '#/components/responses/500-internal-server-error'

  /pmt_rail_net/registrations/{registrationId}/handles:
    get:
      tags:
        - Interac
      summary: Retrieve a handle registration.
      description: >-
        This service allows a participant to retrieve the payment options available for creditor's handles.
      operationId: getCreditorHandles
      security:
        - oAuth2:
            - pmt:registration-management:read
      parameters:
        - $ref: '#/components/parameters/registrationId'
        - $ref: '#/components/parameters/HandleType'
        - $ref: '#/components/parameters/HandleValue'
        - $ref: '#/components/parameters/Traceparent'
        - $ref: '#/components/parameters/Tracestate'
        - $ref: '#/components/parameters/Bncbusinesstraceid'
        - $ref: '#/components/parameters/Accept'
        - $ref: '#/components/parameters/RequestId'
        - $ref: '#/components/parameters/ChannelId'
        - $ref: '#/components/parameters/ChannelType'
        - $ref: '#/components/parameters/ClientId'
        - $ref: '#/components/parameters/ClientAgentId'
        - $ref: '#/components/parameters/AgentId'

      responses:
        200:
          description: OK - If the resource has been fetched successfuly.
          content:
            application/vnd.ca.bnc.pmt+json:
              schema:
                $ref: '#/components/schemas/HandlesResponse'
        400: {"$ref": "#/components/responses/400-bad-request"}
        404: {"$ref": "#/components/responses/404-not-found"}
        500: {"$ref": "#/components/responses/500-internal-server-error"}

  /pmt_rail_net/registrations/{registrationId}/auto-deposit:

    parameters:
      - $ref: "#/components/parameters/registrationId"
      - $ref: '#/components/parameters/Traceparent'
      - $ref: '#/components/parameters/Tracestate'
      - $ref: '#/components/parameters/Bncbusinesstraceid'
      - $ref: '#/components/parameters/Accept'
      - $ref: '#/components/parameters/RequestId'
      - $ref: '#/components/parameters/ChannelId'
      - $ref: '#/components/parameters/ChannelType'
      - $ref: '#/components/parameters/ClientId'
      - $ref: '#/components/parameters/ClientAgentId'
      - $ref: '#/components/parameters/AgentId'
    post:
      security:
        - oAuth2:
            - pmt:registration-management:create
      tags:
        - Interac
      description: >-
        This service can be used by a participant to allow their customer
        to register an account alias for one of their bank accounts, in order to facilitate
        specific Interac e-Transfer operations, such as funds Autodeposit, etc.
      summary: Register to AutoDeposit
      operationId: addAutoDepositRegistration
      requestBody:
        content:
          application/vnd.ca.bnc.pmt+json:
            schema:
              $ref: '#/components/schemas/DomesticAutoDepositRegAdd'
      responses:
        201:
          $ref: '#/components/responses/201-created-ad'
        400:
          $ref: '#/components/responses/400-bad-request'
        500:
          $ref: '#/components/responses/500-internal-server-error'
    get:
      parameters:
        - $ref: "#/components/parameters/pageNumber"
        - $ref: "#/components/parameters/pageSize"
        - $ref: "#/components/parameters/autoDepositRegistrationType"
        - $ref: "#/components/parameters/phoneNumber"
        - $ref: "#/components/parameters/emailAddress"

      security:
        - oAuth2:
            - pmt:registration-management:read
      tags:
        - Interac
      description: >-
        This service can be used by a participant to retrieve a list of
        the existing account alias registrations for one of thier customers based
        on specific search criteria.
      summary: Retrieve AutoDeposit by search criteria.
      operationId: getAutoDepositRegistrations
      responses:
        200:
          $ref: '#/components/responses/AutoDepositRegistrationList'
        404:
          $ref: '#/components/responses/404-not-found'
        400:
          $ref: '#/components/responses/400-bad-request'
        500:
          $ref: '#/components/responses/500-internal-server-error'

  /pmt_rail_net/registrations/{registrationId}/auto-deposit/{autoDepositRegistrationId}:
    parameters:
      - $ref: "#/components/parameters/registrationId"
      - $ref: "#/components/parameters/autoDepositRegistrationId"
      - $ref: '#/components/parameters/Traceparent'
      - $ref: '#/components/parameters/Tracestate'
      - $ref: '#/components/parameters/Bncbusinesstraceid'
      - $ref: '#/components/parameters/Accept'
      - $ref: '#/components/parameters/RequestId'
      - $ref: '#/components/parameters/ChannelId'
      - $ref: '#/components/parameters/ChannelType'
      - $ref: '#/components/parameters/ClientId'
      - $ref: '#/components/parameters/ClientAgentId'
      - $ref: '#/components/parameters/AgentId'
    get:
      security:
        - oAuth2:
            - pmt:registration-management:read
      tags:
        - Interac
      description: >-
        This service can be used by a participant to retrieve a list of
        the existing account alias registrations for one of thier customers based
        on specific search criteria.
      summary: Retrieve AutoDeposit by id.
      operationId: getAutoDepositRegistration
      responses:
        200:
          $ref: '#/components/responses/AutoDepositRegistrationView'
        404:
          $ref: '#/components/responses/404-not-found'
        400:
          $ref: '#/components/responses/400-bad-request'
        500:
          $ref: '#/components/responses/500-internal-server-error'
    put:
      security:
        - oAuth2:
            - pmt:registration-management:update
      tags:
        - Interac
      description: >-
        Update an existing auto deposit registration for a customer.
      summary: Update AutoDeposit.
      operationId: updateAutoDepositRegistration
      requestBody:
        content:
          application/vnd.ca.bnc.pmt+json:
            schema:
              $ref: '#/components/schemas/DomesticAutoDepositRegUpdate'
      responses:
        204:
          $ref: '#/components/responses/204-no-content'
        404:
          $ref: '#/components/responses/404-not-found'
        400:
          $ref: '#/components/responses/400-bad-request'
        500:
          $ref: '#/components/responses/500-internal-server-error'
    patch:
      security:
        - oAuth2:
            - pmt:registration-management:delete
      tags:
        - Interac
      description: >-
        This service can be used by a participant to soft delete a particular
        auto deposit registration for a customer.
      summary: Update status autoDeposit.
      operationId: softDeleteAutoDepositReg
      requestBody:
        content:
          application/vnd.ca.bnc.pmt+json:
            schema:
              $ref: '#/components/schemas/DomesticAutoDepositRegPatch'
      responses:
        204:
          $ref: '#/components/responses/204-no-content'
        404:
          $ref: '#/components/responses/404-not-found'
        400:
          $ref: '#/components/responses/400-bad-request'
        500:
          $ref: '#/components/responses/500-internal-server-error'

components:
  parameters:
    Traceparent:
      in: header
      name: traceparent
      required: true
      schema:
        $ref: '#/components/schemas/Traceparent'
      description: >-
        HTTP header containing information about the incoming request in a distributed tracing system. The traceparent header uses the version-trace_id-parent_id-trace_flags format where:
          * version is always 00.
          * trace_id is a hex-encoded trace id.
          * span_id is a hex-encoded span id.
          * trace_flags is a hex-encoded 8-bit field that contains tracing flags such as sampling, trace level, etc.

          Ref: https://www.w3.org/TR/trace-context/#traceparent-header

    Tracestate:
      in: header
      name: tracestate
      required: false
      schema:
        $ref: '#/components/schemas/Tracestate'
      description: >-
        HTTP header is to provide additional vendor-specific trace identification information across different distributed tracing systems and is a companion header for the traceparent field. It also conveys information about the request’s position in multiple distributed tracing graphs.
        
        Ref: https://www.w3.org/TR/trace-context/#tracestate-header, https://www.w3.org/TR/trace-context/#tracestate-limits

    Bncbusinesstraceid:
      in: header
      name: bncbusinesstraceid
      required: true
      schema:
        $ref: '#/components/schemas/Bncbusinesstraceid'
      description: >-
        HTTP header is to provide additional vendor-specific trace business identification information across different distributed systems.  This reference contains a Universally Unique IDentifier (UUID) compliant with version 4 of standard RFC4122.

    Accept:
      in: header
      name: accept
      required: true
      schema:
        $ref: '#/components/schemas/Accept'
      description: >-
        Header used by the gateway for routing between versions of the endpoint. Explicitly added for clarity.

    RequestId:
      in: header
      name: x-request-id
      required: true
      example: 3bcdf7fa-7e48-4565-9751-d8acbdb64d8b
      schema:
        $ref: '#/components/schemas/RequestId'
      description: >-
        Unique ID generated for each request used for message tracking purposes (E2E).
        Technical and unique traceability identifier internal to the Bank. Used by monitoring and log tolls such as Datadog and Splunk.

    ChannelId:
      in: header
      name: x-channel-id
      required: true
      example: 5156
      schema:
        $ref: '#/components/schemas/ChannelId'
      description: >-
        The source code of the application performing an action on the resource.

    ChannelType:
      in: header
      name: x-channel-type
      required: true
      example: WEB
      schema:
        $ref: '#/components/schemas/ChannelType'
      description: >-
        The type of channel performing an action on the resource.

    ClientId:
      in: header
      name: x-client-id
      required: true
      schema:
        $ref: '#/components/schemas/ClientId'
      description: >-
        The id of the client.

    ClientAgentId:
      in: header
      name: x-client-agent-id
      required: false
      schema:
        $ref: '#/components/schemas/ClientAgentId'
      description: >-
        The id of the authenticated employee performing an action on behalf of his employer, who is a commercial client of the bank.

    AgentId:
      in: header
      name: x-agent-id
      example: abcd123
      schema:
        $ref: '#/components/schemas/AgentId'
      description: >-
        The id for the on behalf transaction.  

    pageNumber:
      in: query
      name: pageNumber
      schema:
        type: integer
        minimum: 1
        default: 1
      description: >-
        Pagination. Participant can indicate the page number to retrieve. Default value is first page.

    RegistrationTypeFilter:
      in: query
      name: registrationType
      required: false
      schema:
        type: array
        items:
          $ref: '#/components/schemas/RegistrationType'
      description: >-
        Registration type desired.

    pageSize:
      in: query
      name: pageSize
      schema:
        description: >-
          Pagination. Participant can indicate the number of creditors to return per page. Default value is used otherwise.
        type: integer
        minimum: 1
        maximum: 500
        default: 200
      description: The numbers of items per page
    autoDepositRegistrationType:
      in: query
      name: autoDepositRegistrationType
      description: >-
        <br/> EMAIL - means Email based auto deposit registration
        <br/> PHONE - means Phone based auto deposit registration
      schema:
        $ref: "#/components/schemas/AutoDepositRegistrationType"
    phoneNumber:
      in: query
      name: phoneNumber
      description: >-
        If autoDepositRegistrationType is PHONE - Customer's phone number is used to uniquely identify
        the auto deposit  registration across the system. </br>
      schema:
        $ref: "#/components/schemas/PhoneNumber"
    emailAddress:
      in: query
      name: emailAddress
      description: >-
        If autoDepositRegistrationType is EMAIL - Customer's email address used to uniquely identify
        the auto deposit registration across the system. <br/>
      schema:
        $ref: '#/components/schemas/EmailAddress'

    HandleType:
      in: query
      name: handleType
      required: true
      schema:
        $ref: '#/components/schemas/HandleType'
      description: >-
        HandleType. Will carry the type of handle, can be email or sms.

    HandleValue:
      in: query
      name: handleValue
      required: true
      schema:
        $ref: '#/components/schemas/HandleValue'
      description: >-
        HandleValue. Will carry the value of handle, can be email or phone number
          to send a sms.

    registrationId:
      name: registrationId
      in: path
      required: true
      description: Client's registration identifier.
      schema:
        type: string

    autoDepositRegistrationId:
      description: Account Alias Registration Reference Number at Interac.
      in: path
      name: autoDepositRegistrationId
      required: true
      schema:
        type: string
        minLength: 8
        maxLength: 35
        example: "CATUcarp"

  requestBodies:
    ClientRegistrationRequest:
      required: true
      description: Client registration.
      content:
        application/vnd.ca.bnc.pmt+json:
          schema:
            $ref: '#/components/schemas/ClientRegistrationInput'
  responses:
    AutoDepositRegistrationList:
      description: OK
      content:
        application/vnd.ca.bnc.pmt+json:
          schema:
            $ref: '#/components/schemas/AutoDepositRegistrationList'
    AutoDepositRegistrationView:
      description: OK
      content:
        application/vnd.ca.bnc.pmt+json:
          schema:
            $ref: '#/components/schemas/ViewAutoDepositRegistration'

    ClientRegistrationResponse:
      description: OK.
      content:
        application/vnd.ca.bnc.pmt+json:
          schema:
            $ref: '#/components/schemas/Registrations'

    201-created:
      description: CREATED - If the request has succeeded and has led to the creation of a resource.
      content:
        'application/vnd.ca.bnc.pmt+json':
          schema:
            $ref: '#/components/schemas/ClientRegistrationId'

    201-created-ad:
      description: CREATED - If the request has succeeded and has led to the creation of a resource.
      content:
        'application/vnd.ca.bnc.pmt+json':
          schema:
            $ref: '#/components/schemas/ClientADRegistrationId'

    204-no-content:
      description: NO CONTENT - If the request has succeeded and has led to action on the resource.

    400-bad-request:
      description: BAD REQUEST - If the request is not valid.
      content:
        'application/vnd.ca.bnc.pmt+json':
          schema:
            $ref: "#/components/schemas/Errors"

    404-not-found:
      description: RESOURCE NOT FOUND

    500-internal-server-error:
      description: INTERNAL SERVER ERROR - If the server encountered an unexpected condition.
      content:
        application/vnd.ca.bnc.pmt+json:
          schema:
            $ref: "#/components/schemas/Errors"

  schemas:
    Traceparent:
      type: string
      minLength: 55
      maxLength: 55
      example: '00-80e1afed08e019fc1110464cfa66635c-7a085853722dc6d2-01'

    Tracestate:
      type: string
      minLength: 1
      maxLength: 512
      example: 'vendorname1=opaqueValue1,vendorname2=opaqueValue2'

    Bncbusinesstraceid:
      type: string
      format: uuid-4
      example: '3bcdf7fa-7e48-4565-9751-d8acbdb64d8b'
      minLength: 36
      maxLength: 36

    Accept:
      type: string
      example: 'application/vnd.ca.bnc.pmt+json;version=v2'

    RequestId:
      type: string
      format: uuid-4
      example: '3bcdf7fa-7e48-4565-9751-d8acbdb64d8b'
      minLength: 36
      maxLength: 36

    ChannelId:
      type: string
      example: '5156'
      minLength: 1
      maxLength: 4

    ChannelType:
      type: string
      enum: [ 'WEB', 'MOBILE' ]
      example: 'WEB'

    ClientId:
      type: string
      example: '2C811E20AEE80A2860764937595FFE76DBF72788941F0C7726CC626949350900'
      minLength: 1
      maxLength: 128

    ClientAgentId:
      type: string
      example: '12BD2E5746C6013778F4CD84D4533FAE6CF80C8C6F52C686691562F452D5FAZ0'
      minLength: 1
      maxLength: 128

    AgentId:
      type: string
      example: 'abcd123'
      minLength: 1

    Registrations:
      type: object
      description: Returns a list of registrations.
      properties:
        registrations:
          type: array
          items:
            $ref: '#/components/schemas/ClientRegistration'

    ClientRegistrationInput:
      type: object
      required:
        - endToEndBusinessIdentification
        - railRegistration
      properties:
        endToEndBusinessIdentification:
          $ref: '#/components/schemas/EndToEndBusinessIdentification'
        registrationType:
          $ref: '#/components/schemas/RegistrationType'
        railRegistration:
          type: array
          items:
            $ref: '#/components/schemas/RailRegistrationItem'
          description: List of all the registered payment rails for the client.
        clientInformationData:
          $ref: '#/components/schemas/ClientInformationData'
    RailRegistrationItem:
      type: object
      required:
        - railType
      properties:
        railType:
          type: string
          enum:
            - DOMESTIC_INTERAC
            - INTERNATIONAL
            - ABP
          description: Type of the registered payment rail.
        registrationName:
          type: string
          minLength: 1
          maxLength: 80
          description: Client’s primary registration name/alias. If null, the client's name is used.

    ClientInformationData:
      type: object
      properties:
        nationality:
          $ref: '#/components/schemas/CountryCode'
        simplified:
          $ref: '#/components/schemas/Simplified'
        advanced:
          $ref: '#/components/schemas/Advanced'

    Simplified:
      type: object
      properties:
        contactDetails:
          $ref: '#/components/schemas/ContactDetailsSimplified'

    Advanced:
      type: object
      required:
        - clientName
        - contactDetails
      properties:
        clientName:
          $ref: '#/components/schemas/ClientName'
        clientType:
          $ref: '#/components/schemas/ClientType'
        contactDetails:
          $ref: '#/components/schemas/ContactDetails'
        dateOfBirth:
          description: Required if railType INTERNATIONAL is selected.
          type: string
          format: date

    RegistrationType:
      description: Indicates registration type.
      type: string
      enum:
        - INTERAC

    ClientType:
      description: Indicates party type.
      type: string
      enum:
        - INDIVIDUAL
        - ORGANISATION

    ClientName:
      oneOf:
        - $ref: '#/components/schemas/IndividualName'
        - $ref: '#/components/schemas/OrganisationName'
    IndividualName:
      description: Required if client’s clientType is INDIVIDUAL.
      type: object
      required:
        - firstName
        - lastName
      properties:
        firstName:
          description: Client's first/given name.
          type: string
          minLength: 1
          maxLength: 100
          example: John
        middleName:
          description: Client's middle name.
          type: string
          minLength: 1
          maxLength: 100
          example: Jo
        lastName:
          description: Client’s last name.
          type: string
          minLength: 1
          maxLength: 100
          example: Doe
    OrganisationName:
      description: Required if client’s clientType is ORGANISATION.
      type: object
      required:
        - companyName
      properties:
        companyName:
          description: Client's company name.
          type: string
          minLength: 1
          maxLength: 100
          example: John Doe inc.
        tradeName:
          description: Client's trade name.
          type: string
          minLength: 1
          maxLength: 100
          example: John Doe
    ContactDetails:
      description: Must provide the language preferences, and the email address (mandatory) for the notifications.
      type: object
      required:
        - language
        - emailAddress
      properties:
        postalAddress:
          $ref: '#/components/schemas/PostalAddress'
        emailAddress:
          $ref: '#/components/schemas/EmailAddress'
        phoneNumber:
          $ref: '#/components/schemas/PhoneNumber'
        language:
          $ref: '#/components/schemas/Language'

    ContactDetailsSimplified:
      type: object
      properties:
        emailAddress:
          $ref: '#/components/schemas/EmailAddress'
        language:
          $ref: '#/components/schemas/Language'

    Language:
      type: string
      enum:
        - EN
        - FR
      description: Client’s language preference code.

    PostalAddress:
      description: Must be present if railType INTERNATIONAL selected.
      type: object
      required:
        - addressType
      properties:
        addressType:
          $ref: '#/components/schemas/AddressType'
        department:
          description: Identification of a division of a large organisation or building.
          type: string
          minLength: 1
          maxLength: 70
        subDepartment:
          description: Identification of a sub-division of a large organisation or building.
          type: string
          minLength: 1
          maxLength: 70
        streetName:
          description: Street number and street name.
          type: string
          minLength: 1
          maxLength: 70
        buildingNumber:
          description: Number that identifies the position of a building on a street.
          type: string
          minLength: 1
          maxLength: 16
        postCode:
          description: Postal code.
          type: string
          minLength: 1
          maxLength: 16
        townName:
          description: Town or city.
          type: string
          minLength: 1
          maxLength: 35
        countrySubDivision:
          description: Province or State.
          type: string
          minLength: 1
          maxLength: 35
        country:
          $ref: '#/components/schemas/CountryCode'
        addressLine:
          description: If needed these fields can be used to specify the address in a free format text style.
          type: string
          minLength: 1
          maxLength: 70
    AddressType:
      description: >-
        Identifies the nature of the postal address. Type of address expressed as a code.
      type: string
      enum: ['ADDR', 'PBOX', 'HOME', 'BIZZ', 'MLTO', 'DLVY']
      example: 'ADDR'
    ClientRegistrationId:
      type: object
      required:
        - registrationId
      properties:
        registrationId:
          description: Client's registration identifier.
          type: string
          example: "9091e5ab0a194cd6ad1eab60b7e5579e"
    ClientADRegistrationId:
      type: object
      required:
        - autoDepositregistrationId
      properties:
        autoDepositRegistrationId:
          description: Auto Deposit Registration Reference Number at Interac.
          type: string
          example: "CATUcarpdrfe"
    ClientRegistration:
      type: object
      required:
        - registrationId
        - clientType
        - registrationType
        - railRegistration
      properties:
        registrationId:
          description: Client's registration identifier.
          type: string
          example: b079f58292f641eb9550725f34304ccc
        clientType:
          $ref: '#/components/schemas/ClientType'
        registrationType:
          $ref: '#/components/schemas/RegistrationType'
        railRegistration:
          type: array
          items:
            $ref: '#/components/schemas/RailRegistrationItem'
          description: List of all the registered payment rails for the client.
        contactDetails:
          $ref: '#/components/schemas/ContactDetailsSimplified'
        nationality:
          $ref: '#/components/schemas/CountryCode'

    HandleType:
      description: >-
        HandleType. Will carry the type of handle, can be email or sms.
      type: string
      enum: ['EMAIL', 'PHONE']
      example: 'EMAIL'

    HandleValue:
      description: >-
        HandleValue. Will carry the value of handle, can be email or phone number
          to send a sms.
      type: string
      example: '<EMAIL>'
      minLength: 1

    HandlesResponse:
      description: Handles Response
      type: object
      required:
        - handleType
        - handleValue
        - paymentOptions
      properties:
        handleType:
          type: string
          enum: ['EMAIL', 'PHONE']
          example: 'EMAIL'
        handleValue:
          type: string
          minLength: 1
          example: '<EMAIL>'
        paymentOptions:
          type: array
          items:
            $ref: '#/components/schemas/PaymentOption'

    PaymentOption:
      description: Payment Option
      type: object
      required:
        - paymentType
      properties:
        paymentType:
          $ref: '#/components/schemas/PaymentType'
        accountAliasReference:
          description: Account alias Registration reference number generated by interac,
            for using at payment initiation
          type: string
          minLength: 8
          maxLength: 35
          example: 'nn8bfihz210722175029769cl5fxu6qa'
        clientName:
          $ref: '#/components/schemas/ClientName'

    PaymentType:
      description: >-
        REGULAR_PAYMENT                  - Regular "Send-Money" payment </br>
        ACCOUNT_ALIAS_PAYMENT            - Auto-Deposit payment </br>
        REALTIME_ACCOUNT_ALIAS_PAYMENT   - Real-time Auto-Deposit payment </br>
        ACCOUNT_DEPOSIT_PAYMENT          - Account-Deposit payment </br>
        REALTIME_ACCOUNT_DEPOSIT_PAYMENT - Real-time Account-Deposit payment </br>
      type: string
      enum: ['REGULAR_PAYMENT', 'ACCOUNT_ALIAS_PAYMENT',
             'REALTIME_ACCOUNT_ALIAS_PAYMENT', 'ACCOUNT_DEPOSIT_PAYMENT', 'REALTIME_ACCOUNT_DEPOSIT_PAYMENT']
      example: 'ACCOUNT_ALIAS_PAYMENT'
    DomesticAutoDepositRegAdd:
      type: object
      properties:
        endToEndBusinessIdentification:
          $ref: '#/components/schemas/EndToEndBusinessIdentification'
        autoDepositRegistrationType:
          $ref: '#/components/schemas/AutoDepositRegistrationType'
        autoDepositRegistration:
          $ref: '#/components/schemas/AutoDepositRegistration'
        accountHolderName:
          $ref: '#/components/schemas/ClientName'
        creditorAccount:
          type: object
          properties:
            identification:
              $ref: '#/components/schemas/Identification'
            creationDate:
              example: 2020-01-23T12:34:56.123Z
              type: string
              format: date-time
          required:
            - identification
      required:
        - endToEndBusinessIdentification
        - autoDepositRegistration
        - contactDetails
        - creditorAccount
        - autoDepositRegistrationType
        - accountHolderName
    CreditorContact:
      description: >-
        Set of elements used to indicate how to contact the party.
        The elements in this block are used to specify the additional party details such as email and mobile phone number.
      type: object
      properties:
        phoneNumber:
          $ref: '#/components/schemas/PhoneNumber'
        emailAddress:
          $ref: '#/components/schemas/EmailAddress'
    EmailAddress:
      description: >-
        Identifies the party’s address for electronic mail (e-mail).</br>
      type: string
      example: <EMAIL>
    PhoneNumber:
      type: string
      description: >-
        Identifies the party mobile phone number. </br>
        (up to 10 characters).</br>
      example: **********
    Identification:
      description: >-
        Account number. </br>
          006 or 612 is the Institution Id (fixed length 3 digits)
        bbbbb is the Transit Number (fixed length 5 digits)
        ccccccc or cccccccccccc is the bank account number (fixed length 7 or fixed length 12)
      type: string
      example: 006-bbbbb-ccccccc or 612-bbbbb-cccccccccccc

    AutoDepositRegistrationType:
      description: >-
        Flag indicating the type of the auto deposit registration
        <br/> EMAIL - means Email based auto deposit registration and the emailAddress must be provided in the following object autoDepositRegistration.
        <br/> PHONE - means Phone based auto deposit registration and the phoneNumber must be provided in the following object autoDepositRegistration.
      type: string
      enum: ['EMAIL', 'PHONE']
      x-example: EMAIL
    AutoDepositRegistration:
      allOf:
        - $ref: '#/components/schemas/CreditorContact'
    EndToEndBusinessIdentification:
      description: EndToEndBusinessIdentification is a unique reference cross-domain provide by the channel.
        This reference contains a Universally Unique IDentifier (UUID) compliant with version 4 of standard RFC4122 without dashes.
      type: string
      example: '3bcdf7fa7e4845659751d8acbdb64d8b'
      minLength: 1
      maxLength: 32

    Errors:
      type: object
      description: Returns a list of errors.
      properties:
        errors:
          type: array
          items:
            $ref: '#/components/schemas/Error'
    Error:
      description: Error code.
      type: object
      title: Error
      required:
        - code
        - text
        - origin
      properties:
        code:
          description: Error Source Code.
          title: Code
          type: string
          maxLength: 50
          example: "CLIENT_NOT_EXIST"
        text:
          description: Description.
          title: Text
          type: string
          maxLength: 2000
          example: "Error description"
        origin:
          description: Error source.
          title: Origin
          type: string
          maxLength: 50
          example: "pmt-registration-api"
        rule:
          description: Business rule.
          title: Rule
          type: string
          maxLength: 50
          example: "R_IEC_AUT_001"
    ViewAutoDepositRegistration:
      description: "View Auto Deposit Registration Request"
      allOf:
        - $ref: "#/components/schemas/AutoDepositAddReg"
        - type: object
          required:
            - autoDepositRegistrationStatus
            - autoDepositRegistrationId
          properties:
            autoDepositRegistrationId:
              $ref: "#/components/schemas/AccountAliasReference"
            autoDepositRegistrationStatus:
              #description: Current status of the account alias registration. Not required for POST operations.
              $ref: '#/components/schemas/AutoDepositRegistrationStatus'
            accountHolderName:
              $ref: '#/components/schemas/AccountHolderName'
            clientName:
              $ref: '#/components/schemas/ClientName'
    AutoDepositAddReg:
      type: object
      properties:
        endToEndBusinessIdentification:
          $ref: '#/components/schemas/EndToEndBusinessIdentification'
        autoDepositRegistrationType:
          $ref: '#/components/schemas/AutoDepositRegistrationType'
        autoDepositRegistration:
          $ref: '#/components/schemas/AutoDepositRegistration'
        creditorAccount:
          type: object
          properties:
            identification:
              $ref: '#/components/schemas/Identification'
          required:
            - identification
        autoDepositRegistrationId:
          $ref: "#/components/schemas/AccountAliasReference"
        autoDepositRegistrationStatus:
          $ref: '#/components/schemas/AutoDepositRegistrationStatus'
      required:
        - endToEndBusinessIdentification
        - autoDepositRegistrationType
        - autoDepositRegistration
        - creditorAccount
        - autoDepositRegistrationId
        - autoDepositRegistrationStatus
    AutoDepositRegistrationList:
      type: object
      description: Returns a list of Auto deposit registration for the customer.
      properties:
        autoDepositRegistrations:
          type: array
          items:
            $ref: '#/components/schemas/ViewAutoDepositRegistration'
        paging:
          type: object
          properties:
            first:
              description: >-
                relative url rendering first page of results.
              type: string
              example: "./autoDepositRegistrations?pageNumber=1&pageSize=20"
            previous:
              description: >-
                relative url rendering previous page of results.
              type: string
              example: "./autoDepositRegistrations?pageNumber=3&pageSize=20"
            next:
              description: >-
                relative url rendering next page of results.
              type: string
              example: "./autoDepositRegistrations?pageNumber=4&pageSize=20"
    AccountHolderName:
      description: Account holder name.
      type: string
      minLength: 1
      maxLength: 80
      example: John Doe
    AccountAliasReference:
      description: Auto Deposit Registration Reference Number at Interac.
      type: string
      minLength: 8
      maxLength: 35
      example: "CATUcarp"
    AutoDepositRegistrationStatus:
      description: >-
        'Flag indicating the customers auto deposit  registration status <br/>
          PENDING - means auto deposit registration is pending verification and confirmation
        from customer. <br/>
          ACTIVE -  means auto deposit registration is active and can be used for
        transfers. <br/>
          IN_REVIEW - means auto deposit registration is being verified by Interac
        for fraud purposes. <br/>
      type: string
      enum: ['PENDING', 'ACTIVE', 'IN_REVIEW']
      example: "PENDING"
    DomesticAutoDepositRegUpdate:
      type: object
      properties:
        endToEndBusinessIdentification:
          $ref: '#/components/schemas/EndToEndBusinessIdentification'
        originalEndToEndBusinessIdentification:
          $ref: '#/components/schemas/EndToEndBusinessIdentification'
        accountHolderName:
          $ref: '#/components/schemas/ClientName'
        creditorAccount:
          type: object
          properties:
            identification:
              $ref: '#/components/schemas/Identification'
          required:
            - identification
      required:
        - endToEndBusinessIdentification
        - originalEndToEndBusinessIdentification
        - creditorAccount
    DomesticAutoDepositRegPatch:
      type: object
      properties:
        endToEndBusinessIdentification:
          $ref: '#/components/schemas/EndToEndBusinessIdentification'
        autoDepositRegistrationStatus:
          $ref: '#/components/schemas/AutoDepositRegDeclinedStatus'
      required:
        - endToEndBusinessIdentification
        - autoDepositRegistrationStatus
    AutoDepositRegDeclinedStatus:
      description: >-
        'To indicate the status change for the AD registration <br/>
      type: string
      enum: ['DECLINED']
      example: "DECLINED"
    CountryCode:
      description: Only ISO 3166 Alpha-2 codes are allowed.
      type: string
      enum: ['AA', 'AB', 'AD', 'AE', 'AF', 'AG', 'AI', 'AL', 'AM', 'AN', 'AO', 'AQ',
             'AR', 'AS', 'AT', 'AU', 'AW', 'AX', 'AZ', 'BA', 'BB', 'BD', 'BE', 'BF', 'BG',
             'BH', 'BI', 'BJ', 'BL', 'BM', 'BN', 'BO', 'BQ', 'BR', 'BS', 'BT', 'BV', 'BW',
             'BY', 'BZ', 'C2', 'CA', 'CC', 'CD', 'CF', 'CG', 'CH', 'CI', 'CK', 'CL', 'CM',
             'CN', 'CO', 'CR', 'CU', 'CV', 'CW', 'CX', 'CY', 'CZ', 'DE', 'DJ', 'DK', 'DM',
             'DO', 'DZ', 'EC', 'EE', 'EG', 'EH', 'ER', 'ES', 'ET', 'FI', 'FJ', 'FK', 'FM',
             'FO', 'FR', 'GA', 'GB', 'GD', 'GE', 'GF', 'GG', 'GH', 'GI', 'GL', 'GM', 'GN',
             'GP', 'GQ', 'GR', 'GS', 'GT', 'GU', 'GW', 'GY', 'HK', 'HM', 'HN', 'HR', 'HT',
             'HU', 'ID', 'IE', 'IL', 'IM', 'IN', 'IO', 'IQ', 'IR', 'IS', 'IT', 'JE', 'JM',
             'JO', 'JP', 'K1', 'KE', 'KG', 'KH', 'KI', 'KM', 'KN', 'KP', 'KR', 'KW', 'KY',
             'KZ', 'LA', 'LB', 'LC', 'LI', 'LK', 'LR', 'LS', 'LT', 'LU', 'LV', 'LY', 'MA',
             'MC', 'MD', 'ME', 'MF', 'MG', 'MH', 'MK', 'ML', 'MM', 'MN', 'MO', 'MP', 'MQ',
             'MR', 'MS', 'MT', 'MU', 'MV', 'MW', 'MX', 'MY', 'MZ', 'NA', 'NC', 'NE', 'NF',
             'NG', 'NI', 'NL', 'NO', 'NP', 'NR', 'NU', 'NZ', 'OM', 'PA', 'PE', 'PF', 'PG',
             'PH', 'PK', 'PL', 'PM', 'PN', 'PR', 'PS', 'PT', 'PW', 'PY', 'QA', 'QM', 'QN',
             'QO', 'QP', 'QQ', 'QR', 'QS', 'QT', 'QU', 'QV', 'QW', 'QX', 'QY', 'QZ', 'RE',
             'RO', 'RS', 'RU', 'RW', 'S1', 'SA', 'SB', 'SC', 'SD', 'SE', 'SG', 'SH', 'SI',
             'SJ', 'SK', 'SL', 'SM', 'SN', 'SO', 'SR', 'SS', 'ST', 'SV', 'SX', 'SY', 'SZ',
             'TC', 'TD', 'TF', 'TG', 'TH', 'TJ', 'TK', 'TL', 'TM', 'TN', 'TO', 'TP', 'TR',
             'TT', 'TV', 'TW', 'TZ', 'UA', 'UG', 'UM', 'US', 'UY', 'UZ', 'VA', 'VC', 'VE',
             'VG', 'VI', 'VN', 'VU', 'WF', 'WS', 'XA', 'XB', 'XD', 'XE', 'XF', 'XG', 'XN',
             'XP', 'XQ', 'XR', 'XS', 'XT', 'XU', 'XV', 'XW', 'XY', 'XZ', 'YE', 'YT', 'YU',
             'ZA', 'ZM', 'ZW']
  # SECURITY
  securitySchemes:
    oAuth2:
      type: oauth2
      description: This API uses OAuth 2 with the implicit grant flow.
      flows:
        authorizationCode: # ou un autre flux OAuth2 comme implicit, password, clientCredentials
          authorizationUrl:  https://api-ti.bnc.ca/bnc/ti-out/sso/oauth2/ausb2snfqxccuEfI90h7/v1/authorize
          tokenUrl: https://api-ti.bnc.ca/bnc/ti-out/sso/oauth2/ausb2snfqxccuEfI90h7/v1/token
          scopes:
            pmt:registration-management:read : read resource
            pmt:registration-management:create : create resource
            pmt:registration-management:update : update resource
            pmt:registration-management:delete : delete resource
