<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 https://maven.apache.org/xsd/settings-1.0.0.xsd">

    <servers>
        <server>
            <id>mirror</id>
            <username>${env.MAVEN_USERNAME}</username>
            <password>${env.MAVEN_PASSWORD}</password>
        </server>
        <server>
            <id>dev</id>
            <username>${env.MAVEN_USERNAME}</username>
            <password>${env.MAVEN_PASSWORD}</password>
        </server>
        <server>
            <id>staging</id>
            <username>${env.MAVEN_USERNAME}</username>
            <password>${env.MAVEN_PASSWORD}</password>
        </server>
        <server>
            <id>production</id>
            <username>${env.MAVEN_USERNAME}</username>
            <password>${env.MAVEN_PASSWORD}</password>
        </server>
        <server>
            <id>nexus6866</id>
            <username>${env.MAVEN_USERNAME}</username>
            <password>${env.MAVEN_PASSWORD}</password>
        </server>
        <server>
            <id>maven-central</id>
            <username>${env.MAVEN_USERNAME}</username>
            <password>${env.MAVEN_PASSWORD}</password>
        </server>
        <server>
            <id>releases</id>
            <username>${env.PUBLISH_RELEASE_MAVEN_USERNAME}</username>
            <password>${env.PUBLISH_RELEASE_MAVEN_PASSWORD}</password>
        </server>
    </servers>

    <mirrors>
        <mirror>
            <id>mirror</id>
            <mirrorOf>*,!dev,!staging,!production,!nexus6866,!maven-central</mirrorOf>
            <url>https://nexus.bnc.ca/repository/mvn-bnc-central/</url>
        </mirror>
    </mirrors>

    <profiles>
        <profile>
            <id>prod</id>
            <repositories>
                <repository>
                    <id>staging</id>
                    <name>Repo staging</name>
                    <url>https://nexus.bnc.ca/repository/7873-mvn-staging-local/</url>
                </repository>
                <repository>
                    <id>production</id>
                    <name>Repo prod</name>
                    <url>https://nexus.bnc.ca/repository/7873-mvn-production-local/</url>
                </repository>
                <repository>
                    <id>nexus6866</id>
                    <name>Repo staging</name>
                    <url>https://nexus.bnc.ca/repository/6866-mvn-staging-local/</url>
                </repository>
                <repository>
                    <id>maven-central</id>
                    <name>Maven central</name>
                    <url>https://nexus.bnc.ca/repository/maven-central/</url>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>production</id>
                    <name>Repo prod</name>
                    <url>https://nexus.bnc.ca/repository/7873-mvn-production-local/</url>
                </pluginRepository>
                <pluginRepository>
                    <id>maven-central</id>
                    <name>Maven central</name>
                    <url>https://nexus.bnc.ca/repository/maven-central/</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>
        <profile>
            <id>sonar</id>
            <properties>
                <sonar.host.url>https://sonar.bnc.ca</sonar.host.url>
                <sonar.projectKey>APP7873.ca.bnc.payment:pmt-etransfer-payment-processing-simulation-api</sonar.projectKey>
                <sonar.moduleKey>${project.groupId}:${project.artifactId}</sonar.moduleKey>
            </properties>
        </profile>
    </profiles>
    <activeProfiles>
        <activeProfile>prod</activeProfile>
        <activeProfile>sonar</activeProfile>
    </activeProfiles>
</settings>