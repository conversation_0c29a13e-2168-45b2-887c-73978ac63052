<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Payment processing simulation" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ACTIVE_PROFILES" value="local" />
    <envs>
      <env name="SPRING_PROFILE_INCLUDE" value="logging-full" />
      <env name="DISABLE_SSL_VALIDATION" value="true" />
    </envs>
    <module name="pmt-etransfer-payment-processing-simulation-api-service" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="ca.bnc.payment.Application" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>