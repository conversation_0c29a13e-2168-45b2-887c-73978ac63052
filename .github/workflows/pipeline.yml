name: Pipeline

on: [ push ]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false
jobs:
  CI:
    name: pipeline
    uses: NBC-Cards/apidc-common-workflows/.github/workflows/pipeline_java.yml@v3.0.28
    with:
      application-name: 'pmt-processing-simulation-api'
      ecr_image_name: 'pmt-processing-simulation-api'
      java_version: '17'
      java_distribution: 'temurin'
      publish_jar: true # set me to false if you don't want to package the jar
      environment: "non_production"
      sonar_quality_gate: 'Sonar way BNC' # could be removed. Default value is 'Sonar way'
      sonar_quality_profile: 'java:Sonar way BNC'
      sonar_project_key: 'APP7873.ca.bnc.payment:pmt-etransfer-payment-processing-simulation-api'
      sonar_project_name: 'pmt-etransfer-payment-processing-simulation-api'
      skip-sanity-test: true
      skip-e2e-test: true
      domain: 'payments'
      jar_settings_repo_id: 'dev'
      deployment-ecs-fargate: false
      publish_from_feature_branch: false # pour les etapes de publish
      deploy-from-feature-branch: false # pour les etapes de deploy sur EKS
      publish_ecr: false
      bounded_context: 'et-pmt-proc'
      component_test_target: 'component-test'
      use_mvn_local_settings: true
      #bnc
      prefix-path-helm-value-files: 'deployment'
      deployment-shared-eks: true
      publish_jib: true
      substage: 'tu'

    secrets: inherit