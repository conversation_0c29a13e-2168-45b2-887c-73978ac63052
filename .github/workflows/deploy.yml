name: CD BNC (EKS)

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read  # This is required for actions/checkout

on:
  workflow_dispatch:
    inputs:
      artifact-version:
        description: 'version to deploy'
        required: true
        type: string
      environment:
        description: 'git environment'
        required: true
        type: choice
        options:
          - 'non_production'
          - 'production'
        default: 'non_production'
      stage:
        description: 'deployment stage'
        type: choice
        required: true
        options:
          - ''
          - 'development'
          - 'staging'
          - 'prod'
        default: ''
      substage:
        description: 'deployment substage'
        type: choice
        required: true
        options:
          - ''
          - 'tu'
          - 'ti'
          - 'ta'
          - 'pp'
        default: 'tu'
      deploy-dr:
        description: 'deploy disaster recovery DR'
        type: boolean
        required: true
        default: false
jobs:
  CD:
    name: CD BNC (EKS)
    uses: NBC-Cards/apidc-cd-workflows/.github/workflows/workflows-continuous-delivery.yml@v3.5.5
    with:
      application-name: 'pmt-processing-simulation-api'
      artifact-version: ${{ inputs.artifact-version }}
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      substage: ${{ inputs.substage }}
      deploy-dr: ${{ inputs.deploy-dr }}
      deployment-shared-eks: true
      domain: 'payments'
      bounded_context: 'et-pmt-proc'
      skip-sanity-test: true
      skip-e2e-test: true
      prefix-path-helm-value-files: 'deployment'
    secrets: inherit
