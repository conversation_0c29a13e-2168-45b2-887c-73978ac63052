name: Release

on:
  workflow_dispatch:
    inputs:
      version_bump_type:
        description: 'version bump type after creating the branch release'
        required: true
        type: string
        options:
          - 'major'
          - 'minor'
          - 'patch'
        default: 'patch'

# enable me when everything will be completed . disabled for now to avoid concurrency issue as we 're on make it work mode
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

jobs:
  release:
    name: release
    uses: NBC-Cards/apidc-ci-java-workflows/.github/workflows/release.yml@v1.2.11
    with:
      base_branch: 'main'
    secrets: inherit

  bump:
    needs: release
    name: bump
    uses: NBC-Cards/apidc-ci-java-workflows/.github/workflows/bump.yml@v1.2.11
    with:
      release_type: ${{ inputs.version_bump_type }}
      branch: 'main'
    secrets: inherit