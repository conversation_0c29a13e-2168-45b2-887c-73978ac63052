package ca.bnc.payment.service;

import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith({MockitoExtension.class})
class PaymentSimulationOrchestratorTest {

    private PaymentSimulationOrchestrator paymentSimulationOrchestrator;

    @Mock
    private PaymentRequestValidationService paymentRequestValidationService;

    @Mock
    private InteracRegistrationValidationService interacRegistrationValidationService;

    @Mock
    private LimitsValidationService limitsValidationService;

    @Mock
    private MoneyRequestValidationService moneyRequestValidationService;

    @Mock
    private BankAccountConnectorService bankAccountConnectorService;

    @BeforeEach
    void setup() {
        paymentSimulationOrchestrator = new PaymentSimulationOrchestrator(
                paymentRequestValidationService,
                interacRegistrationValidationService,
                limitsValidationService,
                moneyRequestValidationService,
                bankAccountConnectorService
        );
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(paymentRequestValidationService, interacRegistrationValidationService, limitsValidationService);
    }

    @Test
    void shouldSimulatePayment() {
        final SendPaymentExecuteRequest paymentRequest = Instancio.create(SendPaymentExecuteRequest.class);
        final String endToEndBusinessIdentification = Instancio.create(String.class);
        final PaymentSimulationHeader paymentSimulationHeader = Instancio.create(PaymentSimulationHeader.class);

        paymentSimulationOrchestrator.simulatePayment(
                paymentRequest,
                endToEndBusinessIdentification,
                paymentSimulationHeader
        );

        then(paymentRequestValidationService)
                .should()
                .validatePaymentRequest(argThat(context -> isExpectedContext(context, paymentRequest, endToEndBusinessIdentification,
                        paymentSimulationHeader)));
        then(interacRegistrationValidationService)
                .should()
                .validateInteracRegistration(argThat(context -> isExpectedContext(context, paymentRequest,
                        endToEndBusinessIdentification, paymentSimulationHeader)));
        then(limitsValidationService)
                .should()
                .validateLimits(argThat(context -> isExpectedContext(context, paymentRequest, endToEndBusinessIdentification,
                        paymentSimulationHeader)));
        then(moneyRequestValidationService)
                .should()
                .validateIncomingMoneyRequest(argThat(context -> isExpectedContext(context, paymentRequest, endToEndBusinessIdentification,
                        paymentSimulationHeader)));
        then(bankAccountConnectorService)
                .should()
                .initiateDebit(argThat(context -> isExpectedContext(context, paymentRequest, endToEndBusinessIdentification,
                        paymentSimulationHeader)), eq(endToEndBusinessIdentification));
    }

    private boolean isExpectedContext(
            PaymentSimulationRequestContext context,
            SendPaymentExecuteRequest expectedRequest,
            String expectedEndToEndId,
            PaymentSimulationHeader expectedHeader) {

        return context.paymentRequest().equals(expectedRequest)
                && context.endToEndBusinessIdentification().equals(expectedEndToEndId)
                && context.paymentSimulationHeader().equals(expectedHeader);
    }

}
