package ca.bnc.payment.service;

import ca.bnc.payment.adapter.limitsvelocity.LimitsVelocityAdapter;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willThrow;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith({MockitoExtension.class})
class LimitsValidationServiceTest {

    private LimitsValidationService limitsValidationService;

    @Mock
    private LimitsVelocityAdapter limitsVelocityAdapter;

    @BeforeEach
    void setUp() {
        limitsValidationService = new LimitsValidationService(limitsVelocityAdapter);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(limitsVelocityAdapter);
    }

    @Test
    void validateLimits() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.create(PaymentSimulationRequestContext.class);
        limitsValidationService.validateLimits(paymentSimulationRequestContext);
        then(limitsVelocityAdapter).should().simulateVelocities(paymentSimulationRequestContext);
    }


    @Test
    void limitsVelocityOtherException() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.create(PaymentSimulationRequestContext.class);
        final RuntimeException runtimeException = Instancio.create(RuntimeException.class);
        willThrow(runtimeException).given(limitsVelocityAdapter).simulateVelocities(paymentSimulationRequestContext);

        assertThatThrownBy(() -> limitsValidationService.validateLimits(paymentSimulationRequestContext))
                .isInstanceOfSatisfying(RuntimeException.class,
                        e -> {
                            assertThat(e).isNotNull();
                            assertThat(e).isEqualTo(runtimeException);
                        });
    }
}
