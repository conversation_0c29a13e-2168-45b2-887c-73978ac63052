package ca.bnc.payment.service;

import ca.bnc.payment.adapter.paymentregistration.PaymentRegistrationAdapter;
import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_payment_registration_resources.generated.model.ClientRegistration;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_payment_registration_resources.generated.model.RailRegistrationItem;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_payment_registration_resources.generated.model.Registrations;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType;
import org.instancio.Instancio;
import org.instancio.TypeToken;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.stream.Stream;

import static ca.bnc.payment.utils.ErrorConstants.CLIENT_NOT_REGISTERED_CODE;
import static ca.bnc.payment.utils.ErrorConstants.CLIENT_NOT_REGISTERED_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.PAYMENT_REGISTRATION_SERVICE_ORIGIN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracRegistrationValidationServiceTest {

    private static final String X_CHANNEL_ID = Instancio.create(String.class);
    private static final ChannelType X_CHANNEL_TYPE = Instancio.create(ChannelType.class);
    private static final String X_CLIENT_ID = Instancio.create(String.class);
    private static final UUID X_REQUEST_ID = Instancio.create(UUID.class);
    private static final String TRACEPARENT = Instancio.create(String.class);

    private ResponseEntity<Registrations> getRegistrationsResponse;

    private InteracRegistrationValidationService interacRegistrationValidationService;

    @Mock
    private PaymentRegistrationAdapter paymentRegistrationAdapter;

    @BeforeEach
    public void setup() {
        interacRegistrationValidationService = new InteracRegistrationValidationService(paymentRegistrationAdapter);
        getRegistrationsResponse = Instancio.create(new TypeToken<>() {
        });
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(paymentRegistrationAdapter);
    }

    @Test
    void validateInteracRegistration() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.create(PaymentSimulationRequestContext.class);
        Objects.requireNonNull(getRegistrationsResponse.getBody()).getRegistrations().get(0).getRailRegistration().get(0).setRailType(RailRegistrationItem.RailTypeEnum.DOMESTIC_INTERAC);
        given(paymentRegistrationAdapter.getRegistrations(paymentSimulationRequestContext)).willReturn(getRegistrationsResponse);
        interacRegistrationValidationService.validateInteracRegistration(paymentSimulationRequestContext);
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("getInvalidRegistrations")
    @DisplayName("should throw InteracRegistrationValidationException when ")
    void invalidRegistrationScenarios(Consumer<ResponseEntity<Registrations>> setup) {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationHeader.class, "traceparent"), TRACEPARENT)
                .set(field(PaymentSimulationHeader.class, "xRequestId"), X_REQUEST_ID)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), X_CHANNEL_ID)
                .set(field(PaymentSimulationHeader.class, "xChannelType"), X_CHANNEL_TYPE)
                .set(field(PaymentSimulationHeader.class, "xClientId"), X_CLIENT_ID)
                .create();
        setup.accept(getRegistrationsResponse);
        given(paymentRegistrationAdapter.getRegistrations(paymentSimulationRequestContext)).willReturn(getRegistrationsResponse);
        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(CLIENT_NOT_REGISTERED_CODE,
                CLIENT_NOT_REGISTERED_LOG,
                PAYMENT_REGISTRATION_SERVICE_ORIGIN,
                NA
        );
        assertThatThrownBy(() -> interacRegistrationValidationService.validateInteracRegistration(paymentSimulationRequestContext))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));
    }

    static Stream<Arguments> getInvalidRegistrations() {
        return Stream.of(
                Arguments.argumentSet("null registrations", (Consumer<ResponseEntity<Registrations>>)
                        response -> Objects.requireNonNull(response.getBody()).setRegistrations(null)),
                Arguments.argumentSet("empty registrations", (Consumer<ResponseEntity<Registrations>>)
                        response -> Objects.requireNonNull(response.getBody()).getRegistrations().clear()),
                Arguments.argumentSet("invalid payment rail type", (Consumer<ResponseEntity<Registrations>>)
                        response -> {
                    List<ClientRegistration> registrations = Objects.requireNonNull(response.getBody()).getRegistrations();
                    for (ClientRegistration registration : registrations) {
                        registration.getRailRegistration().forEach(railRegistration ->
                                railRegistration.setRailType(RailRegistrationItem.RailTypeEnum.INTERNATIONAL)
                        );
                    }
                })
        );
    }

    @Test
    void paymentRegistrationApiReturnsNullBody() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationHeader.class, "traceparent"), TRACEPARENT)
                .set(field(PaymentSimulationHeader.class, "xRequestId"), X_REQUEST_ID)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), X_CHANNEL_ID)
                .set(field(PaymentSimulationHeader.class, "xChannelType"), X_CHANNEL_TYPE)
                .set(field(PaymentSimulationHeader.class, "xClientId"), X_CLIENT_ID)
                .create();
        given(paymentRegistrationAdapter.getRegistrations(paymentSimulationRequestContext)).willReturn(ResponseEntity.ok(null));

        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(CLIENT_NOT_REGISTERED_CODE,
                CLIENT_NOT_REGISTERED_LOG,
                PAYMENT_REGISTRATION_SERVICE_ORIGIN,
                NA
        );
        assertThatThrownBy(() -> interacRegistrationValidationService.validateInteracRegistration(paymentSimulationRequestContext))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));
    }
}
