package ca.bnc.payment.service;

import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.bnc.payment.validator.PaymentRequestValidator;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class PaymentRequestValidationServiceTest {

    private PaymentRequestValidationService paymentRequestValidationService;

    @Mock
    private PaymentRequestValidator mockValidator;

    @BeforeEach
    void setUp() {
        paymentRequestValidationService = new PaymentRequestValidationService(List.of(mockValidator));
    }

    @AfterEach
    void verifyNoUndesiredInteractions() {
        verifyNoMoreInteractions(mockValidator);
    }

    @Test
    void validateWithApplicableTrue() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.create(PaymentSimulationRequestContext.class);
        given(mockValidator.isApplicable(paymentSimulationRequestContext)).willReturn(Boolean.TRUE);

        paymentRequestValidationService.validatePaymentRequest(paymentSimulationRequestContext);

        then(mockValidator).should().validate(paymentSimulationRequestContext);
    }

    @Test
    void validateWithApplicableFalse() {
        SendPaymentExecuteRequest paymentRequest = Instancio.create(SendPaymentExecuteRequest.class);
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationRequestContext::paymentRequest), paymentRequest)
                .create();
        given(mockValidator.isApplicable(paymentSimulationRequestContext)).willReturn(Boolean.FALSE);

        paymentRequestValidationService.validatePaymentRequest(paymentSimulationRequestContext);

    }

}
