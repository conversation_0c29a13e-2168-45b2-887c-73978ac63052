package ca.bnc.payment.service;

import ca.bnc.payment.adapter.bankaccountconnector.BankAccountConnectorAdapter;
import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.*;
import ca.bnc.payment.utils.PaymentRequestUtil;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ChannelType;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentType;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;
import java.util.stream.Stream;

import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.*;

@ExtendWith({MockitoExtension.class})
class BankAccountConnectorServiceTest {
    private static final String END_TO_END_BUSINESS_IDENTIFICATION = Instancio.create(String.class);

    @Mock
    private PaymentRequestUtil paymentRequestUtil;

    @Mock
    private BankAccountConnectorAdapter bankAccountConnectorAdapter;

    private BankAccountConnectorService bankAccountConnectorService;

    @BeforeEach
    void setUp() {
        bankAccountConnectorService = new BankAccountConnectorService(paymentRequestUtil, bankAccountConnectorAdapter);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(paymentRequestUtil, bankAccountConnectorAdapter);
    }

    @ParameterizedTest(name = "payloadChannelType = {0}, headerChannelType = {1}, paymentType = {2}, expectedChannelType = {3}")
    @MethodSource("channelAndPaymentTypeTestCases")
    void shouldPassCorrectChannelAndPaymentTypesToAdapter(
            MandateRelatedSupplementaryData.OriginalChannelTypeEnum payloadChannelType,
            ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType headerChannelType,
            PaymentType paymentType,
            ChannelType expectedChannelType) {

        LocalInstrument2Choice.ProprietaryEnum proprietaryEnum = LocalInstrument2Choice.ProprietaryEnum.fromValue(paymentType.getValue());

        PaymentSimulationRequestContext context = createRequestContext(headerChannelType, proprietaryEnum);

        if (headerChannelType == ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType.BATCH) {
            given(paymentRequestUtil.getOriginalChannelType(context.paymentRequest()))
                    .willReturn(payloadChannelType);
        }
        given(paymentRequestUtil.getProprietary(context.paymentRequest()))
                .willReturn(proprietaryEnum);

        bankAccountConnectorService.initiateDebit(context, END_TO_END_BUSINESS_IDENTIFICATION);

        then(bankAccountConnectorAdapter).should().initiateDebit(
                END_TO_END_BUSINESS_IDENTIFICATION,
                context,
                expectedChannelType,
                paymentType
        );
    }

    private PaymentSimulationRequestContext createRequestContext(
            ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType headerChannelType,
            LocalInstrument2Choice.ProprietaryEnum proprietaryEnum) {

        LocalInstrument2Choice localInstrument = Instancio.of(LocalInstrument2Choice.class)
                .set(field(LocalInstrument2Choice::getProprietary), proprietaryEnum)
                .create();

        PaymentTypeInformation28 paymentTypeInformation = Instancio.of(PaymentTypeInformation28.class)
                .set(field(PaymentTypeInformation28::getLocalInstrument), localInstrument)
                .create();

        CreditTransferTransaction39 creditTransferTransaction = Instancio.of(CreditTransferTransaction39.class)
                .set(field(CreditTransferTransaction39::getPaymentTypeInformation), paymentTypeInformation)
                .create();

        FIToFICustomerCreditTransferV08 fiToFICustomerCreditTransfer = Instancio.of(FIToFICustomerCreditTransferV08.class)
                .set(field(FIToFICustomerCreditTransferV08::getCreditTransferTransactionInformation), creditTransferTransaction)
                .create();

        PaymentSimulationHeader header = Instancio.of(PaymentSimulationHeader.class)
                .set(field(PaymentSimulationHeader::xChannelType), headerChannelType)
                .set(field(PaymentSimulationHeader::bncBusinessTraceId), Instancio.create(UUID.class).toString())
                .create();

        SendPaymentExecuteRequest paymentRequest = Instancio.of(SendPaymentExecuteRequest.class)
                .set(field(SendPaymentExecuteRequest::getFiToFICustomerCreditTransfer), fiToFICustomerCreditTransfer)
                .create();

        return Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationRequestContext::paymentSimulationHeader), header)
                .set(field(PaymentSimulationRequestContext::paymentRequest), paymentRequest)
                .create();
    }

    static Stream<Arguments> channelAndPaymentTypeTestCases() {
        return Stream.of(
                Arguments.of(MandateRelatedSupplementaryData.OriginalChannelTypeEnum.MOBILE,
                        ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType.BATCH,
                        PaymentType.REGULAR_PAYMENT, ChannelType.MOBILE),
                Arguments.of(MandateRelatedSupplementaryData.OriginalChannelTypeEnum.WEB,
                        ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType.WEB,
                        PaymentType.ACCOUNT_ALIAS_PAYMENT, ChannelType.WEB),
                Arguments.of(MandateRelatedSupplementaryData.OriginalChannelTypeEnum.WEB,
                        ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType.BATCH,
                        PaymentType.REALTIME_ACCOUNT_ALIAS_PAYMENT, ChannelType.WEB),
                Arguments.of(MandateRelatedSupplementaryData.OriginalChannelTypeEnum.MOBILE,
                        ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType.MOBILE,
                        PaymentType.ACCOUNT_DEPOSIT_PAYMENT, ChannelType.MOBILE),
                Arguments.of(MandateRelatedSupplementaryData.OriginalChannelTypeEnum.BATCH,
                        ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType.BATCH,
                        PaymentType.REALTIME_ACCOUNT_DEPOSIT_PAYMENT, ChannelType.BATCH),
                Arguments.of(null,
                        ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType.BATCH,
                        PaymentType.FULFILL_REQUEST_FOR_PAYMENT, ChannelType.BATCH)
        );
    }
}
