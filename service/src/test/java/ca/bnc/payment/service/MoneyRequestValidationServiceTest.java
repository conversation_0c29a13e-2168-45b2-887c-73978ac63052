package ca.bnc.payment.service;

import ca.bnc.payment.adapter.moneyrequest.MoneyRequestAdapter;
import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.model.CreditorPaymentActivationRequest;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.model.DomesticFulfillmentMoneyRequest;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.model.MoneyRequestStatus;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.model.PaymentInformation;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.FIToFICustomerCreditTransferV08;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.PaymentTypeInformation28;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SupplementaryData;
import ca.bnc.payment.utils.ChannelIdUtil;
import ca.bnc.payment.utils.ParticipantIdUtil;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.model.MoneyRequestStatus.AVAILABLE_TO_BE_FULFILLED;
import static ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.model.MoneyRequestStatus.FULFILLED;
import static ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT;
import static ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice.ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT;
import static ca.bnc.payment.utils.ErrorConstants.CANNOT_DUE_REQUEST_STATUS_CODE;
import static ca.bnc.payment.utils.ErrorConstants.INTERAC_MONEY_REQUEST_ID_MISSING_CODE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.instancio.Select.field;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class MoneyRequestValidationServiceTest {

    private MoneyRequestValidationService moneyRequestValidationService;

    @Mock
    private MoneyRequestAdapter mockMoneyRequestAdapter;

    @Mock
    private ChannelIdUtil mockChannelIdUtil;

    @Mock
    private ParticipantIdUtil mockParticipantIdUtil;

    @BeforeEach
    void setUp() {
        moneyRequestValidationService = new MoneyRequestValidationService(
                mockMoneyRequestAdapter,
                mockChannelIdUtil,
                mockParticipantIdUtil
        );
    }

    @AfterEach
    void verifyNoUndesiredInteractions() {
        verifyNoMoreInteractions(mockMoneyRequestAdapter, mockChannelIdUtil, mockParticipantIdUtil);
    }

    @Test
    void validateIncomingMoneyRequest_NotApplicable_NoValidation() {
        final PaymentSimulationRequestContext context = createPaymentSimulationRequestContext();
        given(mockChannelIdUtil.isBneChannelId(context.paymentSimulationHeader().xChannelId())).willReturn(false);

        assertThatCode(() -> moneyRequestValidationService.validateIncomingMoneyRequest(context)).doesNotThrowAnyException();

        then(mockChannelIdUtil).should().isBneChannelId(context.paymentSimulationHeader().xChannelId());
    }

    @Test
    void validateIncomingMoneyRequest_NotBncParticipant_NoValidation() {
        final PaymentSimulationRequestContext context = createPaymentSimulationRequestContext();
        given(mockChannelIdUtil.isBneChannelId(context.paymentSimulationHeader().xChannelId())).willReturn(true);
        given(mockParticipantIdUtil.isBncParticipant()).willReturn(false);

        assertThatCode(() -> moneyRequestValidationService.validateIncomingMoneyRequest(context)).doesNotThrowAnyException();

        then(mockChannelIdUtil).should().isBneChannelId(context.paymentSimulationHeader().xChannelId());
        then(mockParticipantIdUtil).should().isBncParticipant();
    }

    @Test
    void validateIncomingMoneyRequest_NotFulfillRequestForPayment_NoValidation() {
        final PaymentSimulationRequestContext context = createPaymentSimulationRequestContext(ACCOUNT_ALIAS_PAYMENT);
        given(mockChannelIdUtil.isBneChannelId(context.paymentSimulationHeader().xChannelId())).willReturn(true);
        given(mockParticipantIdUtil.isBncParticipant()).willReturn(true);

        assertThatCode(() -> moneyRequestValidationService.validateIncomingMoneyRequest(context)).doesNotThrowAnyException();

        then(mockChannelIdUtil).should().isBneChannelId(context.paymentSimulationHeader().xChannelId());
        then(mockParticipantIdUtil).should().isBncParticipant();
    }

    @Test
    void validateIncomingMoneyRequest_InvalidMoneyRequestStatus_ThrowsException() {
        final PaymentSimulationRequestContext context = createPaymentSimulationRequestContext();
        final DomesticFulfillmentMoneyRequest moneyRequest = createDomesticFulfillmentMoneyRequest(FULFILLED);
        final ResponseEntity<DomesticFulfillmentMoneyRequest> response = ResponseEntity.ok(moneyRequest);

        given(mockChannelIdUtil.isBneChannelId(context.paymentSimulationHeader().xChannelId())).willReturn(true);
        given(mockParticipantIdUtil.isBncParticipant()).willReturn(true);
        given(mockMoneyRequestAdapter.getIncomingMoneyRequest(context)).willReturn(response);

        final PaymentSimulationException exception = assertThrows(PaymentSimulationException.class,
                () -> moneyRequestValidationService.validateIncomingMoneyRequest(context));

        assertThat(exception.getErrorInfo().error().getCode()).isEqualTo(CANNOT_DUE_REQUEST_STATUS_CODE);
        then(mockChannelIdUtil).should().isBneChannelId(context.paymentSimulationHeader().xChannelId());
        then(mockParticipantIdUtil).should().isBncParticipant();
        then(mockMoneyRequestAdapter).should().getIncomingMoneyRequest(context);
    }

    @Test
    void validateIncomingMoneyRequest_ValidMoneyRequestStatus_Success() {
        final PaymentSimulationRequestContext context = createPaymentSimulationRequestContext();
        final DomesticFulfillmentMoneyRequest moneyRequest = createDomesticFulfillmentMoneyRequest(AVAILABLE_TO_BE_FULFILLED);
        final ResponseEntity<DomesticFulfillmentMoneyRequest> response = ResponseEntity.ok(moneyRequest);

        given(mockChannelIdUtil.isBneChannelId(context.paymentSimulationHeader().xChannelId())).willReturn(true);
        given(mockParticipantIdUtil.isBncParticipant()).willReturn(true);
        given(mockMoneyRequestAdapter.getIncomingMoneyRequest(context)).willReturn(response);

        assertThatCode(() -> moneyRequestValidationService.validateIncomingMoneyRequest(context)).doesNotThrowAnyException();

        then(mockChannelIdUtil).should().isBneChannelId(context.paymentSimulationHeader().xChannelId());
        then(mockParticipantIdUtil).should().isBncParticipant();
        then(mockMoneyRequestAdapter).should().getIncomingMoneyRequest(context);
    }

    @Test
    void validateIncomingMoneyRequest_NullMoneyRequestResponse_NoException() {
        final PaymentSimulationRequestContext context = createPaymentSimulationRequestContext();
        final ResponseEntity<DomesticFulfillmentMoneyRequest> response = ResponseEntity.ok(null);

        given(mockChannelIdUtil.isBneChannelId(context.paymentSimulationHeader().xChannelId())).willReturn(true);
        given(mockParticipantIdUtil.isBncParticipant()).willReturn(true);
        given(mockMoneyRequestAdapter.getIncomingMoneyRequest(context)).willReturn(response);

        assertThatCode(() -> moneyRequestValidationService.validateIncomingMoneyRequest(context)).doesNotThrowAnyException();

        then(mockChannelIdUtil).should().isBneChannelId(context.paymentSimulationHeader().xChannelId());
        then(mockParticipantIdUtil).should().isBncParticipant();
        then(mockMoneyRequestAdapter).should().getIncomingMoneyRequest(context);
    }

    @Test
    void validateIncomingMoneyRequest_NullCreditorPaymentActivationRequest_NoException() {
        final PaymentSimulationRequestContext context = createPaymentSimulationRequestContext();
        final DomesticFulfillmentMoneyRequest moneyRequest = new DomesticFulfillmentMoneyRequest();
        moneyRequest.setCreditorPaymentActivationRequest(null);
        final ResponseEntity<DomesticFulfillmentMoneyRequest> response = ResponseEntity.ok(moneyRequest);

        given(mockChannelIdUtil.isBneChannelId(context.paymentSimulationHeader().xChannelId())).willReturn(true);
        given(mockParticipantIdUtil.isBncParticipant()).willReturn(true);
        given(mockMoneyRequestAdapter.getIncomingMoneyRequest(context)).willReturn(response);

        assertThatCode(() -> moneyRequestValidationService.validateIncomingMoneyRequest(context)).doesNotThrowAnyException();

        then(mockChannelIdUtil).should().isBneChannelId(context.paymentSimulationHeader().xChannelId());
        then(mockParticipantIdUtil).should().isBncParticipant();
        then(mockMoneyRequestAdapter).should().getIncomingMoneyRequest(context);
    }

    private PaymentSimulationRequestContext createPaymentSimulationRequestContext() {
        return createPaymentSimulationRequestContext(FULFILL_REQUEST_FOR_PAYMENT);
    }

    private PaymentSimulationRequestContext createPaymentSimulationRequestContext(LocalInstrument2Choice.ProprietaryEnum proprietaryEnum) {
        final PaymentSimulationHeader header = Instancio.create(PaymentSimulationHeader.class);

        final SendPaymentExecuteRequest request = Instancio.of(SendPaymentExecuteRequest.class)
                .set(field(SendPaymentExecuteRequest::getFiToFICustomerCreditTransfer),
                        createFIToFICustomerCreditTransfer(proprietaryEnum, "test-interac-id"))
                .create();

        return new PaymentSimulationRequestContext(request, header, "");
    }

    private PaymentSimulationRequestContext createPaymentSimulationRequestContextWithNullInteracId() {
        final PaymentSimulationHeader header = Instancio.create(PaymentSimulationHeader.class);

        final SendPaymentExecuteRequest request = Instancio.of(SendPaymentExecuteRequest.class)
                .set(field(SendPaymentExecuteRequest::getFiToFICustomerCreditTransfer),
                        createFIToFICustomerCreditTransfer(FULFILL_REQUEST_FOR_PAYMENT, null))
                .create();

        return new PaymentSimulationRequestContext(request, header, "");
    }

    private FIToFICustomerCreditTransferV08 createFIToFICustomerCreditTransfer(LocalInstrument2Choice.ProprietaryEnum proprietaryEnum, String interacId) {
        final SupplementaryData supplementaryData = new SupplementaryData();
        supplementaryData.setInteracMoneyRequestId(interacId);

        final LocalInstrument2Choice localInstrument = new LocalInstrument2Choice();
        localInstrument.setProprietary(proprietaryEnum);

        final PaymentTypeInformation28 paymentTypeInfo = new PaymentTypeInformation28();
        paymentTypeInfo.setLocalInstrument(localInstrument);

        final CreditTransferTransaction39 creditTransferInfo = new CreditTransferTransaction39();
        creditTransferInfo.setSupplementaryData(supplementaryData);
        creditTransferInfo.setPaymentTypeInformation(paymentTypeInfo);

        final FIToFICustomerCreditTransferV08 fiToFICustomerCreditTransfer = new FIToFICustomerCreditTransferV08();
        fiToFICustomerCreditTransfer.setCreditTransferTransactionInformation(creditTransferInfo);

        return fiToFICustomerCreditTransfer;
    }

    private DomesticFulfillmentMoneyRequest createDomesticFulfillmentMoneyRequest(MoneyRequestStatus status) {
        final PaymentInformation paymentInformation = new PaymentInformation();
        paymentInformation.setMoneyRequestStatus(status);

        final CreditorPaymentActivationRequest creditorPaymentActivationRequest = new CreditorPaymentActivationRequest();
        creditorPaymentActivationRequest.setPaymentInformation(paymentInformation);

        final DomesticFulfillmentMoneyRequest moneyRequest = new DomesticFulfillmentMoneyRequest();
        moneyRequest.setCreditorPaymentActivationRequest(creditorPaymentActivationRequest);

        return moneyRequest;
    }
}