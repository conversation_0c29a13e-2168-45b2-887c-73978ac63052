package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditorContact;
import ca.bnc.payment.utils.PaymentRequestUtil;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static ca.bnc.payment.utils.ErrorConstants.CONTACT_DETAILS_INVALID_CODE;
import static ca.bnc.payment.utils.ErrorConstants.CONTACT_DETAILS_INVALID_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;

class PhoneNumberAndEmailAddressBothPresentValidatorTest {

    private PhoneNumberAndEmailAddressBothPresentValidator validator;

    @BeforeEach
    void setUp() {
        PaymentRequestUtil paymentRequestUtil = new PaymentRequestUtil();
        validator = new PhoneNumberAndEmailAddressBothPresentValidator(paymentRequestUtil);
    }

    @Test
    void validate_shouldThrowException_whenEmailAndMobileNumberArePresent() {
        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(CONTACT_DETAILS_INVALID_CODE,
                CONTACT_DETAILS_INVALID_LOG,
                SERVICE_ORIGIN,
                NA
        );

        assertThatThrownBy(() -> validator.validate(createContextWithEmailAndMobileNumber("<EMAIL>", "1234567890")))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));
    }

    @Test
    void validate_shouldNotThrowException_whenEmailOrMobileNumberIsAbsent() {
        assertThatCode(() -> validator.validate(createContextWithEmailAndMobileNumber(null, "1234567890")))
                .doesNotThrowAnyException();
        assertThatCode(() -> validator.validate(createContextWithEmailAndMobileNumber("<EMAIL>", null)))
                .doesNotThrowAnyException();

    }

    private PaymentSimulationRequestContext createContextWithEmailAndMobileNumber(final String email, final String mobileNumber) {
        return  Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(CreditorContact::getEmailAddress), email)
                .set(field(CreditorContact::getMobileNumber), mobileNumber)
                .create();
    }
}