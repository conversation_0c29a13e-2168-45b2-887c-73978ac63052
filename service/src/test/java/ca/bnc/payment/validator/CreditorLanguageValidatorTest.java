package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditorContact;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SupplementaryData;
import ca.bnc.payment.utils.PaymentRequestUtil;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static ca.bnc.payment.utils.ErrorConstants.INVALID_CREDITOR_LANGUAGE_CODE;
import static ca.bnc.payment.utils.ErrorConstants.INVALID_CREDITOR_LANGUAGE_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;

class CreditorLanguageValidatorTest {

    private CreditorLanguageValidator creditorLanguageValidator;

    @BeforeEach
    void setUp() {
        PaymentRequestUtil paymentRequestUtil = new PaymentRequestUtil();
        creditorLanguageValidator = new CreditorLanguageValidator(paymentRequestUtil);
    }

    @Test
    void validate_withMobileNumberAndNullCreditorPreferredLanguage_throwsException() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(CreditorContact::getEmailAddress), null)
                .set(field(CreditorContact::getMobileNumber), "1234567890")
                .set(field(SupplementaryData::getCreditorPreferredLanguage),null)
                .create();
        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(INVALID_CREDITOR_LANGUAGE_CODE,
                INVALID_CREDITOR_LANGUAGE_LOG,
                SERVICE_ORIGIN,
                NA
        );

        assertThatThrownBy(() -> creditorLanguageValidator.validate(paymentSimulationRequestContext))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));
    }

    @Test
    void validate_withEmailAddressAndNullCreditorPreferredLanguage_throwsException() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(CreditorContact::getEmailAddress), "<EMAIL>")
                .set(field(CreditorContact::getMobileNumber), null)
                .set(field(SupplementaryData::getCreditorPreferredLanguage),null)
                .create();

        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(INVALID_CREDITOR_LANGUAGE_CODE,
                INVALID_CREDITOR_LANGUAGE_LOG,
                SERVICE_ORIGIN,
                NA
        );

        assertThatThrownBy(() -> creditorLanguageValidator.validate(paymentSimulationRequestContext))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));
    }

    @Test
    void validate_withMobileNumberAndCreditorPreferredLanguage_doesNotThrowException() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(CreditorContact::getEmailAddress), null)
                .set(field(CreditorContact::getMobileNumber), "1234567890")
                .set(field(SupplementaryData::getCreditorPreferredLanguage),SupplementaryData.CreditorPreferredLanguageEnum.EN)
                .create();
        assertThatCode(() -> creditorLanguageValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();
    }

    @Test
    void validate_withEmailAddressAndCreditorPreferredLanguage_doesNotThrowException() {
       final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(CreditorContact::getEmailAddress), "<EMAIL>")
                .set(field(CreditorContact::getMobileNumber), null)
                .set(field(SupplementaryData::getCreditorPreferredLanguage),SupplementaryData.CreditorPreferredLanguageEnum.EN)
                .create();

        assertThatCode(() -> creditorLanguageValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();

    }

    @Test
    void validate_withNoMobileNumberAndNoEmailAddress_doesNotThrowException() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(CreditorContact::getEmailAddress), null)
                .set(field(CreditorContact::getMobileNumber), null)
                .set(field(SupplementaryData::getCreditorPreferredLanguage),null)
                .create();

        assertThatCode(() -> creditorLanguageValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();
    }
}