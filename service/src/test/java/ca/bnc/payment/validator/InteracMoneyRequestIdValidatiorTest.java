package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SupplementaryData;
import ca.bnc.payment.utils.ChannelIdUtil;
import ca.bnc.payment.utils.ParticipantIdUtil;
import ca.bnc.payment.utils.PaymentRequestUtil;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static ca.bnc.payment.utils.ErrorConstants.INTERAC_MONEY_REQUEST_ID_MISSING_CODE;
import static ca.bnc.payment.utils.ErrorConstants.INTERAC_MONEY_REQUEST_ID_MISSING_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracMoneyRequestIdValidatorTest {

    private InteracMoneyRequestIdValidator interacMoneyRequestIdValidator;

    @Mock
    private ParticipantIdUtil participantIdUtil;

    @Mock
    private ChannelIdUtil channelIdUtil;

    @Mock
    private PaymentRequestUtil paymentRequestUtil;

    @BeforeEach
    void setUp() {
        interacMoneyRequestIdValidator = new InteracMoneyRequestIdValidator(participantIdUtil, channelIdUtil, paymentRequestUtil);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(participantIdUtil, channelIdUtil, paymentRequestUtil);
    }

    @Test
    void isValidWithInteracMoneyRequestId() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(SupplementaryData::getInteracMoneyRequestId), "interacMoneyRequestId123")
                .create();

        assertThatCode(() -> interacMoneyRequestIdValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();
    }

    @Test
    void isValidThrowsPaymentSimulationExceptionWhenInteracMoneyRequestIdIsNull() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(SupplementaryData::getInteracMoneyRequestId), null)
                .create();

        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(
                INTERAC_MONEY_REQUEST_ID_MISSING_CODE,
                INTERAC_MONEY_REQUEST_ID_MISSING_LOG,
                SERVICE_ORIGIN,
                NA
        );

        assertThatThrownBy(() -> interacMoneyRequestIdValidator.validate(paymentSimulationRequestContext))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));
    }

    @Test
    void isApplicableWithNonBNEChannelId() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), "xChannelId")
                .create();
        given(channelIdUtil.isBneChannelId("xChannelId")).willReturn(Boolean.FALSE);

        assertThat(interacMoneyRequestIdValidator.isApplicable(paymentSimulationRequestContext)).isFalse();
    }

    @Test
    void isApplicableWithBNEChannelIdAndBNCParticipantAndFulfillRequestForPayment() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), "8131")
                .create();
        given(channelIdUtil.isBneChannelId("8131")).willReturn(Boolean.TRUE);
        given(participantIdUtil.isBncParticipant()).willReturn(Boolean.TRUE);
        given(paymentRequestUtil.getProprietary(paymentSimulationRequestContext.paymentRequest()))
                .willReturn(LocalInstrument2Choice.ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT);

        assertThat(interacMoneyRequestIdValidator.isApplicable(paymentSimulationRequestContext)).isTrue();
    }

    @Test
    void isApplicableWithBNEChannelIdAndNonBNCParticipant() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), "8131")
                .create();
        given(channelIdUtil.isBneChannelId("8131")).willReturn(Boolean.TRUE);
        given(participantIdUtil.isBncParticipant()).willReturn(Boolean.FALSE);

        assertThat(interacMoneyRequestIdValidator.isApplicable(paymentSimulationRequestContext)).isFalse();
    }

    @Test
    void isApplicableWithBNEChannelIdAndBNCParticipantButNonFulfillRequestForPayment() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), "8131")
                .create();
        given(channelIdUtil.isBneChannelId("8131")).willReturn(Boolean.TRUE);
        given(participantIdUtil.isBncParticipant()).willReturn(Boolean.TRUE);
        given(paymentRequestUtil.getProprietary(paymentSimulationRequestContext.paymentRequest()))
                .willReturn(LocalInstrument2Choice.ProprietaryEnum.REGULAR_PAYMENT);

        assertThat(interacMoneyRequestIdValidator.isApplicable(paymentSimulationRequestContext)).isFalse();
    }

    @Test
    void isApplicableWithNonBNEChannelIdAndBNCParticipantAndFulfillRequestForPayment() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), "nonBNE")
                .create();
        given(channelIdUtil.isBneChannelId("nonBNE")).willReturn(Boolean.FALSE);

        assertThat(interacMoneyRequestIdValidator.isApplicable(paymentSimulationRequestContext)).isFalse();
    }
}