package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.RemittanceInformation16;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static ca.bnc.payment.utils.ErrorConstants.INVALID_REMITTANCE_REGEX_CODE;
import static ca.bnc.payment.utils.ErrorConstants.INVALID_REMITTANCE_REGEX_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

class RemittanceRegExValidatorTest {

    private RemittanceRegExValidator remittanceRegExValidator;

    @BeforeEach
    void setUp() {
        remittanceRegExValidator = new RemittanceRegExValidator();
    }

    @Test
    void validate_withValidRemittance_shouldNotThrowException() {
        PaymentSimulationRequestContext context = createRequestWithRemittance("valid remittance");

        assertDoesNotThrow(() -> remittanceRegExValidator.validate(context));
    }

    @Test
    void validate_withEmptyRemittance_shouldNotThrowException() {
        PaymentSimulationRequestContext context = createRequestWithRemittance();

        assertDoesNotThrow(() -> remittanceRegExValidator.validate(context));
    }

    @Test
    void validate_withRemittanceOutsideRangeEmpty_shouldThrowException() {
        PaymentSimulationRequestContext context = createRequestWithRemittance("");
        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(INVALID_REMITTANCE_REGEX_CODE,
                INVALID_REMITTANCE_REGEX_LOG,
                SERVICE_ORIGIN,
                NA
        );
        assertThatThrownBy(() -> remittanceRegExValidator.validate(context))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));

    }

    @Test
    void validate_withRemittanceOutsideRange_shouldThrowException() {
        PaymentSimulationRequestContext context = createRequestWithRemittance("a".repeat(141));

        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(INVALID_REMITTANCE_REGEX_CODE,
                INVALID_REMITTANCE_REGEX_LOG,
                SERVICE_ORIGIN,
                NA
        );
        assertThatThrownBy(() -> remittanceRegExValidator.validate(context))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));
    }

    @Test
    void validate_withRemittanceInsideRange140char_shouldThrowException() {
        PaymentSimulationRequestContext context = createRequestWithRemittance("a".repeat(140));

        assertDoesNotThrow(() -> remittanceRegExValidator.validate(context));
    }

    @Test
    void validate_withRemittanceInsideRangeOneChar_shouldThrowException() {
        final PaymentSimulationRequestContext context = createRequestWithRemittance("a");
        assertThatCode(() -> remittanceRegExValidator.validate(context)).doesNotThrowAnyException();
    }

    @Test
    void validate_withInvalidRegexRemittance_shouldThrowException() {
        final PaymentSimulationRequestContext context = createRequestWithRemittance("invalid#remittance");
        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(INVALID_REMITTANCE_REGEX_CODE,
                INVALID_REMITTANCE_REGEX_LOG,
                SERVICE_ORIGIN,
                NA
        );
        assertThatThrownBy(() -> remittanceRegExValidator.validate(context))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));
    }

    private PaymentSimulationRequestContext createRequestWithRemittance(String... remittanceLines) {
        return Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(RemittanceInformation16::getUnstructured), List.of(remittanceLines))
                .create();
    }

}