package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SupplementaryData;
import ca.bnc.payment.utils.ChannelIdUtil;
import ca.bnc.payment.utils.ParticipantIdUtil;
import ca.bnc.payment.utils.PaymentRequestUtil;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static ca.bnc.payment.utils.ErrorConstants.PAYMENT_AUTH_REQUEST_INVALID_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.PAYMENT_AUTH_REQUEST_INVALID_CODE;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class AuthenticationInformationValidatorTest {

    private AuthenticationInformationValidator authenticationInformationValidator;
    @Mock
    private ParticipantIdUtil participantIdUtil;
    @Mock
    private ChannelIdUtil channelIdUtil;

    @BeforeEach
    void setUp() {
        PaymentRequestUtil paymentRequestUtil = new PaymentRequestUtil();
        authenticationInformationValidator = new AuthenticationInformationValidator(participantIdUtil, channelIdUtil, paymentRequestUtil);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(participantIdUtil, channelIdUtil);
    }


    @Test
    void isValid() {

        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(LocalInstrument2Choice::getProprietary), LocalInstrument2Choice.ProprietaryEnum.REGULAR_PAYMENT)
                .create();

        assertThatCode(() -> authenticationInformationValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();

    }

    @Test
    void isValidWithRegularPaymentProperty() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(LocalInstrument2Choice::getProprietary), LocalInstrument2Choice.ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT)
                .create();

        assertThatCode(() -> authenticationInformationValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();
    }

    @Test
    void isValidThrowProcessingSimulationException() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(LocalInstrument2Choice::getProprietary), LocalInstrument2Choice.ProprietaryEnum.REGULAR_PAYMENT)
                .set(field(SupplementaryData::getPaymentAuthentication), null)
                .create();

        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(PAYMENT_AUTH_REQUEST_INVALID_CODE,
                PAYMENT_AUTH_REQUEST_INVALID_LOG,
                SERVICE_ORIGIN,
                NA
        );

        assertThatThrownBy(() -> authenticationInformationValidator.validate(paymentSimulationRequestContext))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));
    }

    @Test
    void isApplicableWithNonBNEChannelId() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), "xChannelId")
                .create();
        given(channelIdUtil.isBneChannelId("xChannelId")).willReturn(Boolean.FALSE);

        assertThat(authenticationInformationValidator.isApplicable(paymentSimulationRequestContext)).isFalse();
    }

    @Test
    void isApplicableWithBNEChannelId() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), "8131")
                .create();
        given(channelIdUtil.isBneChannelId("8131")).willReturn(Boolean.TRUE);
        given(participantIdUtil.isBncParticipant()).willReturn(Boolean.TRUE);
        assertThat(authenticationInformationValidator.isApplicable(paymentSimulationRequestContext)).isTrue();
    }

    @Test
    void isApplicableWithBNEChannelIdAndWithNonBNCParticipantId() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), "8131")
                .create();
        given(channelIdUtil.isBneChannelId("8131")).willReturn(Boolean.TRUE);
        given(participantIdUtil.isBncParticipant()).willReturn(Boolean.FALSE);
        assertThat(authenticationInformationValidator.isApplicable(paymentSimulationRequestContext)).isFalse();
    }

}