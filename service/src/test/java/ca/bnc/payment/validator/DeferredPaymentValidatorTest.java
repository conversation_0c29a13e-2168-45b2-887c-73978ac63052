package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditTransferTransaction39;
import ca.bnc.payment.utils.ChannelIdUtil;
import ca.bnc.payment.utils.ParticipantIdUtil;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static ca.bnc.payment.utils.ErrorConstants.MANDATE_RELATED_INFORMATION_MISSING_CODE;
import static ca.bnc.payment.utils.ErrorConstants.MANDATE_RELATED_INFORMATION_MISSING_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class DeferredPaymentValidatorTest {

    private DeferredPaymentValidator deferredPaymentValidator;
    @Mock
    private ParticipantIdUtil participantIdUtil;
    @Mock
    private ChannelIdUtil channelIdUtil;

    @BeforeEach
    void setUp() {
        deferredPaymentValidator = new DeferredPaymentValidator(participantIdUtil, channelIdUtil);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(participantIdUtil, channelIdUtil);
    }


    @Test
    void validate() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .create();

            assertThatCode(() -> deferredPaymentValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();

    }
    @Test
    void inValidateThrowsException() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(CreditTransferTransaction39::getMandateRelatedInformation), null)
                .create();

        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(MANDATE_RELATED_INFORMATION_MISSING_CODE,
                MANDATE_RELATED_INFORMATION_MISSING_LOG,
                SERVICE_ORIGIN,
                NA
        );
        assertThatThrownBy(() -> deferredPaymentValidator.validate(paymentSimulationRequestContext))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));

    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("provideIsApplicableTestCases")
    void isApplicable(boolean isBncParticipant, boolean isDeferredChannelId, boolean expectedResult) {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), "xChannelId")
                .create();
        given(participantIdUtil.isBncParticipant()).willReturn(isBncParticipant);
        given(channelIdUtil.isDeferredChannelId("xChannelId")).willReturn(isDeferredChannelId);

        assertThat(deferredPaymentValidator.isApplicable(paymentSimulationRequestContext)).isEqualTo(expectedResult);
    }

    @Test
    void isApplicable() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), "xChannelId")
                .create();
        given(channelIdUtil.isDeferredChannelId("xChannelId")).willReturn(Boolean.FALSE);

        assertThat(deferredPaymentValidator.isApplicable(paymentSimulationRequestContext)).isFalse();
    }

    private static Stream<Arguments> provideIsApplicableTestCases() {
        return Stream.of(
                Arguments.argumentSet("When isBncParticipant is true and isDeferredChannelId is true", true, true, true),
                Arguments.argumentSet("When isBncParticipant is false and isDeferredChannelId is true", false, true, false));
    }
}