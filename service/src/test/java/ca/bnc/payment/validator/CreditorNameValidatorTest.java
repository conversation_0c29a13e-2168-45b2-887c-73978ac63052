package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditorIdentification;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.bnc.payment.utils.PaymentRequestUtil;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static ca.bnc.payment.utils.ErrorConstants.CREDITOR_NAME_IS_MISSING_LOG;
import static ca.bnc.payment.utils.ErrorConstants.CREDITOR_NAME_MISSING_CODE;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;

class CreditorNameValidatorTest {

    private CreditorNameValidator creditorNameValidator;

    @BeforeEach
    void setUp() {
        PaymentRequestUtil paymentRequestUtil = new PaymentRequestUtil();
        creditorNameValidator = new CreditorNameValidator(paymentRequestUtil);
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("provideValidateTestCases")
    void validate(SendPaymentExecuteRequest sendPaymentExecuteRequest) {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationRequestContext::paymentRequest), sendPaymentExecuteRequest)
                .create();

        assertThatCode(() -> creditorNameValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();

    }

    @Test
    void inValidateThrowsException() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(LocalInstrument2Choice::getProprietary), LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT)
                .set(field(CreditorIdentification::getName), null)
                .create();
        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(CREDITOR_NAME_MISSING_CODE,
                CREDITOR_NAME_IS_MISSING_LOG,
                SERVICE_ORIGIN,
                NA
        );
        assertThatThrownBy(() -> creditorNameValidator.validate(paymentSimulationRequestContext))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));
    }

    @Test
    void isValidWithNonSupportedProprietary() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(LocalInstrument2Choice::getProprietary), LocalInstrument2Choice.ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT)
                .create();

        assertThatCode(() -> creditorNameValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();
    }

    @Test
    void isApplicable() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.create(PaymentSimulationRequestContext.class);
        assertThat(creditorNameValidator.isApplicable(paymentSimulationRequestContext)).isTrue();
    }

    private static Stream<Arguments> provideValidateTestCases() {
        SendPaymentExecuteRequest validRequest = Instancio.of(SendPaymentExecuteRequest.class)
                .set(field(LocalInstrument2Choice::getProprietary), LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT)
                .set(field(CreditorIdentification::getName), "Valid Name")
                .create();

        SendPaymentExecuteRequest nonProprietaryRequest = Instancio.of(SendPaymentExecuteRequest.class)
                .set(field(LocalInstrument2Choice::getProprietary), LocalInstrument2Choice.ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT)
                .set(field(CreditorIdentification::getName), null)
                .create();

        return Stream.of(
                Arguments.argumentSet("When creditor name is present", validRequest),
                Arguments.argumentSet("When proprietary is not supported", nonProprietaryRequest)
        );
    }
}