package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SupplementaryData;
import ca.bnc.payment.utils.PaymentRequestUtil;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static ca.bnc.payment.utils.ErrorConstants.AUTODEPOSIT_REG_NUMBER_MISSING_CODE;
import static ca.bnc.payment.utils.ErrorConstants.AUTODEPOSIT_REG_NUMBER_MISSING_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;

class CreditorAutoDepositRegNumberValidatorTest {

    private CreditorAutoDepositRegNumberValidator creditorAutoDepositRegNumberValidator;

    @BeforeEach
    void setUp() {
        PaymentRequestUtil paymentRequestUtil = new PaymentRequestUtil();
        creditorAutoDepositRegNumberValidator = new CreditorAutoDepositRegNumberValidator(paymentRequestUtil);
    }

    @Test
    void isValid() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(LocalInstrument2Choice::getProprietary), LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT)
                .set(field(SupplementaryData::getCreditorAutoDepositRegNumber), "1234")
                .create();

        assertThatCode(() -> creditorAutoDepositRegNumberValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();

    }

    @Test
    void isValidWithRegularPaymentProperty() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(LocalInstrument2Choice::getProprietary), LocalInstrument2Choice.ProprietaryEnum.REGULAR_PAYMENT)
                .set(field(SupplementaryData::getCreditorAutoDepositRegNumber), "1234")
                .create();

        assertThatCode(() -> creditorAutoDepositRegNumberValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();

    }

    @Test
    void isValidThrowProcessingSimulationException() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(LocalInstrument2Choice::getProprietary), LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT)
                .set(field(SupplementaryData::getCreditorAutoDepositRegNumber), null)
                .create();

        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(AUTODEPOSIT_REG_NUMBER_MISSING_CODE,
                AUTODEPOSIT_REG_NUMBER_MISSING_LOG,
                SERVICE_ORIGIN,
                NA
        );
        assertThatThrownBy(() -> creditorAutoDepositRegNumberValidator.validate(paymentSimulationRequestContext))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));
    }

    @Test
    void isApplicable() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.create(PaymentSimulationRequestContext.class);
        assertThat(creditorAutoDepositRegNumberValidator.isApplicable(paymentSimulationRequestContext)).isTrue();
    }

}
