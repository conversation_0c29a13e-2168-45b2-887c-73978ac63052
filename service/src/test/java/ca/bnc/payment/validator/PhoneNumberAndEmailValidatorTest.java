package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditorContact;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.utils.PaymentRequestUtil;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static ca.bnc.payment.utils.ErrorConstants.CONTACT_DETAILS_MISSING_CODE;
import static ca.bnc.payment.utils.ErrorConstants.CONTACT_DETAILS_MISSING_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;

class PhoneNumberAndEmailValidatorTest {

    private PhoneNumberAndEmailValidator phoneNumberAndEmailValidator;

    @BeforeEach
    void setUp() {
        PaymentRequestUtil paymentRequestUtil = new PaymentRequestUtil();
        phoneNumberAndEmailValidator = new PhoneNumberAndEmailValidator(paymentRequestUtil);
    }

    @Test
    void validateShouldThrowExceptionWhenBothContactDetailsAreMissing() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(LocalInstrument2Choice::getProprietary), LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT)
                .set(field(CreditorContact::getEmailAddress), null)
                .set(field(CreditorContact::getMobileNumber), null)
                .create();

        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(CONTACT_DETAILS_MISSING_CODE,
                CONTACT_DETAILS_MISSING_LOG,
                SERVICE_ORIGIN,
                NA
        );
        assertThatThrownBy(() -> phoneNumberAndEmailValidator.validate(paymentSimulationRequestContext))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));
    }

    @Test
    void validateShouldNotThrowExceptionWhenMobileNumberIsPresent() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(LocalInstrument2Choice::getProprietary), LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT)
                .set(field(CreditorContact::getEmailAddress), null)
                .set(field(CreditorContact::getMobileNumber), "**********")
                .create();
        assertThatCode(() -> phoneNumberAndEmailValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();
    }

    @Test
    void validateShouldNotThrowExceptionWhenEmailAddressIsPresent() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(LocalInstrument2Choice::getProprietary), LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT)
                .set(field(CreditorContact::getEmailAddress), "<EMAIL>")
                .set(field(CreditorContact::getMobileNumber), null)
                .create();
        assertThatCode(() -> phoneNumberAndEmailValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();
    }

    @Test
    void validateShouldNotThrowExceptionWhenProprietaryIsNotAccountAliasPayment() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(LocalInstrument2Choice::getProprietary), LocalInstrument2Choice.ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT)
                .set(field(CreditorContact::getEmailAddress), null)
                .set(field(CreditorContact::getMobileNumber), null)
                .create();

        assertThatCode(() -> phoneNumberAndEmailValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();
    }
}