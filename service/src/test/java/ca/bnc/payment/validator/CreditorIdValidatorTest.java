package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SupplementaryData;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static ca.bnc.payment.utils.ErrorConstants.CREDITOR_ID_MISSING_CODE;
import static ca.bnc.payment.utils.ErrorConstants.CREDITOR_ID_MISSING_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;

class CreditorIdValidatorTest {

    private CreditorIdValidator creditorIdValidator;

    @BeforeEach
    void setUp() {
        creditorIdValidator = new CreditorIdValidator();
    }

    @Test
    void validate_withOrganisationClientTypeAndNullCreditorId_shouldThrowException() {

        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(SupplementaryData::getClientType), SupplementaryData.ClientTypeEnum.ORGANISATION)
                .set(field(SupplementaryData::getCreditorId), null)
                .create();
        final PaymentSimulationException expectedException = PaymentSimulationException.badRequest(CREDITOR_ID_MISSING_CODE,
                CREDITOR_ID_MISSING_LOG,
                SERVICE_ORIGIN,
                NA
        );


        assertThatThrownBy(() -> creditorIdValidator.validate(paymentSimulationRequestContext))
                .isInstanceOfSatisfying(
                        PaymentSimulationException.class,
                        e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));
    }

    @Test
    void validate_withOrganisationClientTypeAndNonNullCreditorId_shouldNotThrowException() {

        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(SupplementaryData::getClientType), SupplementaryData.ClientTypeEnum.ORGANISATION)
                .set(field(SupplementaryData::getCreditorId), UUID.randomUUID())
                .create();
        assertThatCode(() -> creditorIdValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();
    }

    @Test
    void validate_withNonOrganisationClientTypeAndNonNullCreditorId_shouldNotThrowException() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(SupplementaryData::getClientType), SupplementaryData.ClientTypeEnum.INDIVIDUAL)
                .set(field(SupplementaryData::getCreditorId), UUID.randomUUID())
                .create();
        assertThatCode(() -> creditorIdValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();
    }

    @Test
    void validate_withNonOrganisationClientTypeAndCreditorIdNull_shouldNotThrowException() {

        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(SupplementaryData::getClientType), SupplementaryData.ClientTypeEnum.INDIVIDUAL)
                .set(field(SupplementaryData::getCreditorId), null)
                .create();
        assertThatCode(() -> creditorIdValidator.validate(paymentSimulationRequestContext)).doesNotThrowAnyException();
    }
}