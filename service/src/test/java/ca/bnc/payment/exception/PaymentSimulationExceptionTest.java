package ca.bnc.payment.exception;

import org.instancio.Instancio;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static ca.bnc.payment.utils.ErrorConstants.NO_REASON_PROVIDED;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.instancio.Select.field;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;

class PaymentSimulationExceptionTest {

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("provideErrorInfo")
    void shouldTestErrorInfo(final ErrorInfo errorInfo, final String expectedErrorText) {
        PaymentSimulationException exception = new PaymentSimulationException(errorInfo);
        assertThat(exception)
                .returns(errorInfo,PaymentSimulationException::getErrorInfo)
                .returns(expectedErrorText, PaymentSimulationException::getMessage);
    }

    private static Stream<Arguments> provideErrorInfo() {
        final ErrorInfo nonEmptyError = Instancio.create(ErrorInfo.class);
        final ErrorInfo emptyError = Instancio.of(ErrorInfo.class)
                .set(field(ErrorInfo::error),null)
                .create();

        return Stream.of(
                Arguments.argumentSet("Error is not null", nonEmptyError, nonEmptyError.error().getText()),
                Arguments.argumentSet("Error is null", emptyError, NO_REASON_PROVIDED)
        );
    }
}