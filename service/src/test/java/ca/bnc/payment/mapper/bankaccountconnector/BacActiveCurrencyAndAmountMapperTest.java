package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ActiveCurrencyAndAmount;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ActiveCurrencyCode;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.instancio.Select.field;


import java.math.BigDecimal;
import java.math.RoundingMode;

class BacActiveCurrencyAndAmountMapperTest {

    private BacActiveCurrencyAndAmountMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new BacActiveCurrencyAndAmountMapper();
    }

    @Test
    void shouldMapActiveCurrencyAndAmount() {
        BigDecimal amount = Instancio.create(BigDecimal.class).setScale(2, RoundingMode.HALF_EVEN);
        ActiveCurrencyAndAmount activeCurrencyAndAmount = Instancio.of(ActiveCurrencyAndAmount.class)
                .set(field(ActiveCurrencyAndAmount::getAmount), amount)
                .set(field(ActiveCurrencyAndAmount::getCurrency), ActiveCurrencyCode.CAD)
                .create();

        ca.nbc.payment.etransfer.bankaccountconnector.model.ActiveCurrencyAndAmount result = mapper.map(activeCurrencyAndAmount);

        assertThat(result.getAmount()).isEqualByComparingTo(amount);
        assertThat(result.getCurrency()).isEqualTo(ca.nbc.payment.etransfer.bankaccountconnector.model.ActiveCurrencyCode.CAD);
    }

}