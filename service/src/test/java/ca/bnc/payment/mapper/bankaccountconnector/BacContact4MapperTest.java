package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditorContact;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.DebtorContact;
import ca.nbc.payment.etransfer.bankaccountconnector.model.Contact4;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;

@ExtendWith(MockitoExtension.class)
class BacContact4MapperTest {

    private BacContact4Mapper mapper;
    private final String mobileNumber = Instancio.create(String.class);
    private final String emailAddress = Instancio.create(String.class);

    @BeforeEach
    void setUp() {
        mapper = new BacContact4Mapper();
    }

    @Test
    void shouldMapCreditorContactToContact4WithAllFields() {
        final CreditorContact creditorContact = Instancio.of(CreditorContact.class)
                .set(field(CreditorContact::getMobileNumber), mobileNumber)
                .set(field(CreditorContact::getEmailAddress), emailAddress)
                .create();

        final Contact4 result = mapper.map(creditorContact);

        assertThat(result).isNotNull();
        assertThat(result.getMobileNumber()).isEqualTo(mobileNumber);
        assertThat(result.getEmailAddress()).isEqualTo(emailAddress);
    }

    @Test
    void shouldMapCreditorContactToContact4WithNullValues() {
        final CreditorContact creditorContact = Instancio.of(CreditorContact.class)
                .set(field(CreditorContact::getMobileNumber), null)
                .set(field(CreditorContact::getEmailAddress), null)
                .create();

        final Contact4 result = mapper.map(creditorContact);

        assertThat(result).isNotNull();
        assertThat(result.getMobileNumber()).isNull();
        assertThat(result.getEmailAddress()).isNull();
    }

    @Test
    void shouldMapDebtorContactToContact4WithAllFields() {
        final DebtorContact debtorContact = Instancio.of(DebtorContact.class)
                .set(field(DebtorContact::getMobileNumber), mobileNumber)
                .set(field(DebtorContact::getEmailAddress), emailAddress)
                .create();

        final Contact4 result = mapper.map(debtorContact);

        assertThat(result).isNotNull();
        assertThat(result.getMobileNumber()).isEqualTo(mobileNumber);
        assertThat(result.getEmailAddress()).isEqualTo(emailAddress);
    }

    @Test
    void shouldMapDebtorContactToContact4WithNullValues() {
        final DebtorContact debtorContact = Instancio.of(DebtorContact.class)
                .set(field(DebtorContact::getMobileNumber), null)
                .set(field(DebtorContact::getEmailAddress), null)
                .create();

        final Contact4 result = mapper.map(debtorContact);

        assertThat(result).isNotNull();
        assertThat(result.getMobileNumber()).isNull();
        assertThat(result.getEmailAddress()).isNull();
    }

    @Test
    void shouldHandleCreditorContactWithEmptyStrings() {
        final CreditorContact creditorContact = Instancio.of(CreditorContact.class)
                .set(field(CreditorContact::getMobileNumber), "")
                .set(field(CreditorContact::getEmailAddress), "")
                .create();

        final Contact4 result = mapper.map(creditorContact);

        assertThat(result).isNotNull();
        assertThat(result.getMobileNumber()).isEmpty();
        assertThat(result.getEmailAddress()).isEmpty();
    }

    @Test
    void shouldHandleDebtorContactWithEmptyStrings() {
        final DebtorContact debtorContact = Instancio.of(DebtorContact.class)
                .set(field(DebtorContact::getMobileNumber), "")
                .set(field(DebtorContact::getEmailAddress), "")
                .create();

        final Contact4 result = mapper.map(debtorContact);

        assertThat(result).isNotNull();
        assertThat(result.getMobileNumber()).isEmpty();
        assertThat(result.getEmailAddress()).isEmpty();
    }
}