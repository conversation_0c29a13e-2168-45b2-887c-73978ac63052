package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DebitRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.FIToFICustomerDirectDebitV08;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class DebitRequestMapperTest {

    private DebitRequestMapper debitRequestMapper;

    @Mock
    private BacFIToFICustomerDirectDebitV08Mapper bacFIToFICustomerDirectDebitV08Mapper;

    @BeforeEach
    void setUp() {
        debitRequestMapper = new DebitRequestMapper(bacFIToFICustomerDirectDebitV08Mapper);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(bacFIToFICustomerDirectDebitV08Mapper);
    }


    @Test
    void shouldMapToDebitRequest() {
        final String endToEndId = Instancio.create(String.class);
        final String clientId = Instancio.create(String.class);
        final String requestId = Instancio.create(String.class);
        final SendPaymentExecuteRequest request = Instancio.create(SendPaymentExecuteRequest.class);
        final FIToFICustomerDirectDebitV08 fiToFICustomerDirectDebitV08 = Instancio.create(FIToFICustomerDirectDebitV08.class);

        given(bacFIToFICustomerDirectDebitV08Mapper.map(endToEndId, clientId, requestId, request)).willReturn(fiToFICustomerDirectDebitV08);

        DebitRequest result = debitRequestMapper.toDebitRequest(endToEndId, clientId, requestId, request);

        assertThat(result.getFiToFiCustomerDebitTransfer()).isEqualTo(fiToFICustomerDirectDebitV08);
    }
}
