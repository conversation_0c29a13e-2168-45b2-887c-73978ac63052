package ca.bnc.payment.mapper.bankaccountconnector;

import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentIdentification7;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class BacPaymentIdentification7MapperTest {

    private BacPaymentIdentification7Mapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new BacPaymentIdentification7Mapper();
    }

    @Test
    void shouldMapPaymentIdentification() {
        final String endToEndIdentification = Instancio.create(String.class);
        final String instructionIdentification = Instancio.create(String.class);
        ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.PaymentIdentification7 source =
                new ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.PaymentIdentification7();
        source.setEndToEndIdentification(endToEndIdentification);
        source.setInstructionIdentification(instructionIdentification);

        PaymentIdentification7 result = mapper.map(source);

        assertEquals(endToEndIdentification, result.getEndToEndIdentification());
        assertEquals(instructionIdentification, result.getInstructionIdentification());
        assertEquals(endToEndIdentification, result.getTransactionIdentification());
    }

    @Test
    void shouldHandleNullFieldsWhenMapping() {
        ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.PaymentIdentification7 source =
                new ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.PaymentIdentification7();

        PaymentIdentification7 result = mapper.map(source);

        assertNull(result.getEndToEndIdentification());
        assertNull(result.getInstructionIdentification());
        assertNull(result.getTransactionIdentification());
    }
}