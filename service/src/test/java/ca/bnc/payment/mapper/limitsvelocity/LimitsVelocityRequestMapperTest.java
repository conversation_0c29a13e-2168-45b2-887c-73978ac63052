package ca.bnc.payment.mapper.limitsvelocity;

import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.model.ClientType;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.model.LimitType;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.model.SimulationType;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.model.VelocitiesSimulation;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.FIToFICustomerCreditTransferV08;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.PaymentIdentification7;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.PaymentTypeInformation28;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SupplementaryData;
import ca.bnc.payment.utils.IdGenerator;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Clock;
import java.time.OffsetDateTime;
import java.util.stream.Stream;

import static java.time.ZoneOffset.UTC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class LimitsVelocityRequestMapperTest {

    private static final OffsetDateTime OFFSET_DATE_TIME = OffsetDateTime.parse("2025-01-01T00:00:00.000Z");
    private static final Clock clock = Clock.fixed(OFFSET_DATE_TIME.toInstant(), UTC);
    private static final String INSTRUCTION_IDENTIFICATION = "instructionIdentification";

    private LimitsVelocityRequestMapper limitsVelocityRequestMapper;

    @Mock
    private IdGenerator idGenerator;

    @BeforeEach
    public void setup() {
        limitsVelocityRequestMapper = new LimitsVelocityRequestMapper(idGenerator, clock);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(idGenerator);
    }

    @Test
    void simulateVelocities() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.create(PaymentSimulationRequestContext.class);

        VelocitiesSimulation actual = limitsVelocityRequestMapper.mapVelocitiesSimulation(paymentSimulationRequestContext);

        assertThat(actual.getSimulationType()).isEqualTo(SimulationType.VELOCITIES);
        assertThat(actual.getAmount()).isEqualTo(paymentSimulationRequestContext.paymentRequest().getFiToFICustomerCreditTransfer().getCreditTransferTransactionInformation().getInterbankSettlementAmount().getAmount());
        assertThat(actual.getInstructionIdentification()).isEqualTo(paymentSimulationRequestContext.paymentRequest().getFiToFICustomerCreditTransfer().getCreditTransferTransactionInformation().getPaymentIdentification().getInstructionIdentification());
        assertThat(actual.getPostingDate()).isEqualTo(OFFSET_DATE_TIME);
    }

    @Test
    void simulateVelocitiesNoInstructionIdentification() {
        PaymentIdentification7 paymentIdentification = Instancio.of(PaymentIdentification7.class)
                .set(field(PaymentIdentification7::getInstructionIdentification), null).create();
        CreditTransferTransaction39 creditTransferTransaction39 = Instancio.of(CreditTransferTransaction39.class)
                .set(field(CreditTransferTransaction39::getPaymentIdentification), paymentIdentification).create();
        FIToFICustomerCreditTransferV08 fiToFICustomerCreditTransfer = Instancio.of(FIToFICustomerCreditTransferV08.class)
                .set(field(FIToFICustomerCreditTransferV08::getCreditTransferTransactionInformation), creditTransferTransaction39).create();
        SendPaymentExecuteRequest sendPaymentExecuteRequest = Instancio.of(SendPaymentExecuteRequest.class)
                .set(field(SendPaymentExecuteRequest::getFiToFICustomerCreditTransfer), fiToFICustomerCreditTransfer).create();
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationRequestContext::paymentRequest), sendPaymentExecuteRequest).create();

        given(idGenerator.generateInstructionIdentification()).willReturn(INSTRUCTION_IDENTIFICATION);

        VelocitiesSimulation actual = limitsVelocityRequestMapper.mapVelocitiesSimulation(paymentSimulationRequestContext);

        then(idGenerator).should().generateInstructionIdentification();
        assertThat(actual.getSimulationType()).isEqualTo(SimulationType.VELOCITIES);
        assertThat(actual.getAmount()).isEqualTo(paymentSimulationRequestContext.paymentRequest().getFiToFICustomerCreditTransfer().getCreditTransferTransactionInformation().getInterbankSettlementAmount().getAmount());
        assertThat(actual.getInstructionIdentification()).isEqualTo(INSTRUCTION_IDENTIFICATION);
        assertThat(actual.getPostingDate()).isEqualTo(OFFSET_DATE_TIME);
    }

    @ParameterizedTest(name = "{index}: {0}")
    @MethodSource("proprietaryProvider")
    void shouldMapLimitType(LocalInstrument2Choice.ProprietaryEnum proprietary, LimitType expectedLimitType) {
        LocalInstrument2Choice localInstrument2Choice = Instancio.of(LocalInstrument2Choice.class)
                .set(field(LocalInstrument2Choice::getProprietary), proprietary).create();
        PaymentTypeInformation28 paymentTypeInformation = Instancio.of(PaymentTypeInformation28.class)
                .set(field(PaymentTypeInformation28::getLocalInstrument), localInstrument2Choice).create();
        CreditTransferTransaction39 creditTransferTransaction39 = Instancio.of(CreditTransferTransaction39.class)
                .set(field(CreditTransferTransaction39::getPaymentTypeInformation), paymentTypeInformation).create();
        FIToFICustomerCreditTransferV08 fiToFICustomerCreditTransfer = Instancio.of(FIToFICustomerCreditTransferV08.class)
                .set(field(FIToFICustomerCreditTransferV08::getCreditTransferTransactionInformation), creditTransferTransaction39).create();
        SendPaymentExecuteRequest sendPaymentExecuteRequest = Instancio.of(SendPaymentExecuteRequest.class)
                .set(field(SendPaymentExecuteRequest::getFiToFICustomerCreditTransfer), fiToFICustomerCreditTransfer).create();
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationRequestContext::paymentRequest), sendPaymentExecuteRequest).create();

        VelocitiesSimulation result = limitsVelocityRequestMapper.mapVelocitiesSimulation(paymentSimulationRequestContext);

        assertThat(result.getLimitType()).isEqualTo(expectedLimitType);
    }

    private static Stream<Arguments> proprietaryProvider() {
        return Stream.of(
                Arguments.of(LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_DEPOSIT_PAYMENT, LimitType.DOMESTIC_INTERAC_ANR),
                Arguments.of(LocalInstrument2Choice.ProprietaryEnum.REALTIME_ACCOUNT_DEPOSIT_PAYMENT, LimitType.DOMESTIC_INTERAC_RTANR),
                Arguments.of(LocalInstrument2Choice.ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT, LimitType.DOMESTIC_INTERAC_MONEYREQUEST),
                Arguments.of(LocalInstrument2Choice.ProprietaryEnum.REGULAR_PAYMENT, LimitType.DOMESTIC_INTERAC_REGULAR),
                Arguments.of(LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT, LimitType.DOMESTIC_INTERAC_REGULAR),
                Arguments.of(LocalInstrument2Choice.ProprietaryEnum.REALTIME_ACCOUNT_ALIAS_PAYMENT, LimitType.DOMESTIC_INTERAC_REGULAR)
        );
    }

    @ParameterizedTest(name = "{index}: {0}")
    @MethodSource("clientTypeProvider")
    void shouldMapClientType(SupplementaryData.ClientTypeEnum inputClientType, ClientType expectedClientType) {
        SupplementaryData supplementaryData = Instancio.of(SupplementaryData.class)
                .set(field(SupplementaryData::getClientType), inputClientType).create();
        CreditTransferTransaction39 creditTransferTransaction39 = Instancio.of(CreditTransferTransaction39.class)
                .set(field(CreditTransferTransaction39::getSupplementaryData), supplementaryData).create();
        FIToFICustomerCreditTransferV08 fiToFICustomerCreditTransfer = Instancio.of(FIToFICustomerCreditTransferV08.class)
                .set(field(FIToFICustomerCreditTransferV08::getCreditTransferTransactionInformation), creditTransferTransaction39).create();
        SendPaymentExecuteRequest sendPaymentExecuteRequest = Instancio.of(SendPaymentExecuteRequest.class)
                .set(field(SendPaymentExecuteRequest::getFiToFICustomerCreditTransfer), fiToFICustomerCreditTransfer).create();
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationRequestContext::paymentRequest), sendPaymentExecuteRequest).create();

        VelocitiesSimulation result = limitsVelocityRequestMapper.mapVelocitiesSimulation(paymentSimulationRequestContext);

        assertThat(result.getClientType()).isEqualTo(expectedClientType);
    }

    private static Stream<Arguments> clientTypeProvider() {
        return Stream.of(
                Arguments.of(SupplementaryData.ClientTypeEnum.INDIVIDUAL, ClientType.INDIVIDUAL),
                Arguments.of(SupplementaryData.ClientTypeEnum.ORGANISATION, ClientType.ORGANISATION)
        );
    }
}
