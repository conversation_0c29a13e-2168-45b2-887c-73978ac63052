package ca.bnc.payment.mapper.bankaccountconnector;

import ca.nbc.payment.etransfer.bankaccountconnector.model.AccountIdentification4ChoiceOther;
import ca.nbc.payment.etransfer.bankaccountconnector.model.AccountSchemeName1ChoiceProprietary;
import ca.nbc.payment.etransfer.bankaccountconnector.model.CashAccount38;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class BacCashAccount38MapperTest {

    private BacCashAccount38Mapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new BacCashAccount38Mapper();
    }

    @Test
    void shouldReturnNullWhenCashAccountIsNull() {
        CashAccount38 result = mapper.map(null);
        assertThat(result).isNull();
    }

    @Test
    void shouldMapCashAccountToBankAccountConnectorEquivalent() {
        ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CashAccount38 cashAccount =
                Instancio.create(ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CashAccount38.class);

        final CashAccount38 result = mapper.map(cashAccount);

        assertThat(result.getIdentification()).isInstanceOfSatisfying(
                AccountIdentification4ChoiceOther.class,
                actual -> {
                    assertThat(actual.getOther()).isNotNull();
                    assertThat(actual.getOther().getSchemeName()).isInstanceOfSatisfying(
                            AccountSchemeName1ChoiceProprietary.class,
                            proprietary -> assertThat(proprietary.getProprietary())
                                    .isEqualTo(AccountSchemeName1ChoiceProprietary.ProprietaryEnum.BANK_ACCT_NO)
                    );
                }
        );
    }
}