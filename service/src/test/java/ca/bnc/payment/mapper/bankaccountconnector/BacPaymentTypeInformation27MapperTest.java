package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.PaymentTypeInformation28;
import ca.nbc.payment.etransfer.bankaccountconnector.model.LocalInstrument2ChoiceProprietary;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentTypeInformation27;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;

class BacPaymentTypeInformation27MapperTest {

    private BacPaymentTypeInformation27Mapper mapper;

    public static Stream<Arguments> provideAllProprietaryEnums() {
        return Stream.of(
                Arguments.argumentSet("When proprietary enum is Regular Payment", LocalInstrument2Choice.ProprietaryEnum.REGULAR_PAYMENT),
                Arguments.argumentSet("When proprietary enum is Fulfill Request For Payment", LocalInstrument2Choice.ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT),
                Arguments.argumentSet("When proprietary enum is Account Alias Payment", LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT),
                Arguments.argumentSet("When proprietary enum is Realtime Account Alias Payment", LocalInstrument2Choice.ProprietaryEnum.REALTIME_ACCOUNT_ALIAS_PAYMENT),
                Arguments.argumentSet("When proprietary enum is Account Deposit Payment", LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_DEPOSIT_PAYMENT),
                Arguments.argumentSet("When proprietary enum is Realtime Account Deposit Payment", LocalInstrument2Choice.ProprietaryEnum.REALTIME_ACCOUNT_DEPOSIT_PAYMENT)
        );
    }

    @BeforeEach
    void setUp() {
        mapper = new BacPaymentTypeInformation27Mapper();
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("provideAllProprietaryEnums")
    void shouldMapAllProprietaryEnums(LocalInstrument2Choice.ProprietaryEnum sourceEnum) {
        PaymentTypeInformation28 paymentTypeInformation = new PaymentTypeInformation28()
                .localInstrument(new LocalInstrument2Choice().proprietary(sourceEnum));

        PaymentTypeInformation27 result = mapper.map(paymentTypeInformation);

        assertThat(result.getLocalInstrument())
                .isInstanceOfSatisfying(
                        LocalInstrument2ChoiceProprietary.class,
                        proprietary -> {
                            assertThat(proprietary.getObjectType()).isEqualTo("proprietary");
                            assertThat(proprietary.getProprietary().getValue()).isEqualTo(sourceEnum.getValue());
                        }
                );
    }

    @Test
    void shouldThrowExceptionOnNullLocalInstrument() {
        PaymentTypeInformation28 paymentTypeInformation = new PaymentTypeInformation28().localInstrument(null);
        assertThatThrownBy(() -> mapper.map(paymentTypeInformation))
                .isInstanceOf(NullPointerException.class);
    }

    @Test
    void shouldThrowExceptionOnNullProprietaryEnum() {
        PaymentTypeInformation28 paymentTypeInformation = new PaymentTypeInformation28()
                .localInstrument(new LocalInstrument2Choice().proprietary(null));
        assertThatThrownBy(() -> mapper.map(paymentTypeInformation))
                .isInstanceOf(NullPointerException.class);
    }
}