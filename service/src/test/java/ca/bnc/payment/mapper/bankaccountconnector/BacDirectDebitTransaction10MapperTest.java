package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.MandateRelatedInformation;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DirectDebitTransaction10;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class BacDirectDebitTransaction10MapperTest {

    private BacDirectDebitTransaction10Mapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new BacDirectDebitTransaction10Mapper();
    }

    @Test
    void shouldReturnNullWhenMandateRelatedInformationIsNull() {
        DirectDebitTransaction10 result = mapper.map(null);
        assertThat(result).isNull();
    }

    @Test
    void shouldMapMandateRelatedInformationToBankAccountConnectorEquivalent() {
        MandateRelatedInformation mandateRelatedInformation = Instancio.create(MandateRelatedInformation.class);

        DirectDebitTransaction10 result = mapper.map(mandateRelatedInformation);

        assertThat(result.getMandateRelatedInformation()).isNotNull();
        assertThat(result.getMandateRelatedInformation().getMandateIdentification())
                .isEqualTo(mandateRelatedInformation.getMandateIdentification());
    }

}