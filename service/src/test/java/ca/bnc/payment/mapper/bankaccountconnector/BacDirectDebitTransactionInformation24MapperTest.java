package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditTransferTransaction39;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ActiveCurrencyAndAmount;
import ca.nbc.payment.etransfer.bankaccountconnector.model.BranchAndFinancialInstitutionIdentification6;
import ca.nbc.payment.etransfer.bankaccountconnector.model.CashAccount38;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ChargeBearerType1Code;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DirectDebitTransaction10;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DirectDebitTransactionInformation24;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PartyIdentification135;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentIdentification7;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentTypeInformation27;
import ca.nbc.payment.etransfer.bankaccountconnector.model.RemittanceInformation16;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BacDirectDebitTransactionInformation24MapperTest {

    private BacDirectDebitTransactionInformation24Mapper mapper;

    @Mock
    private BacPaymentIdentification7Mapper bacPaymentIdentification7Mapper;
    @Mock
    private BacPaymentTypeInformation27Mapper bacPaymentTypeInformation27Mapper;
    @Mock
    private BacActiveCurrencyAndAmountMapper bacActiveCurrencyAndAmountMapper;
    @Mock
    private BacBranchAndFinancialInstitutionIdentification6Mapper bacBranchAndFinancialInstitutionIdentification6Mapper;
    @Mock
    private BacPartyIdentification135Mapper bacPartyIdentification135Mapper;
    @Mock
    private BacCashAccount38Mapper bacCashAccount38Mapper;
    @Mock
    private BacRemittanceInformation16Mapper bacRemittanceInformation16Mapper;
    @Mock
    private BacDirectDebitTransaction10Mapper bacDirectDebitTransaction10Mapper;


    @BeforeEach
    void setUp() {
        mapper = new BacDirectDebitTransactionInformation24Mapper(
                bacPaymentIdentification7Mapper,
                bacPaymentTypeInformation27Mapper,
                bacActiveCurrencyAndAmountMapper,
                bacBranchAndFinancialInstitutionIdentification6Mapper,
                bacPartyIdentification135Mapper,
                bacCashAccount38Mapper,
                bacRemittanceInformation16Mapper,
                bacDirectDebitTransaction10Mapper
        );
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(
                bacPaymentIdentification7Mapper,
                bacPaymentTypeInformation27Mapper,
                bacActiveCurrencyAndAmountMapper,
                bacBranchAndFinancialInstitutionIdentification6Mapper,
                bacPartyIdentification135Mapper,
                bacCashAccount38Mapper,
                bacRemittanceInformation16Mapper,
                bacDirectDebitTransaction10Mapper
        );
    }

    @Test
    void shouldMapAllFieldsCorrectly() {
        CreditTransferTransaction39 source = Instancio.create(CreditTransferTransaction39.class);

        final PaymentIdentification7 paymentIdentification7 = Instancio.create(PaymentIdentification7.class);
        final PaymentTypeInformation27 paymentTypeInformation27 = Instancio.create(PaymentTypeInformation27.class);
        final ActiveCurrencyAndAmount activeCurrencyAndAmount = Instancio.create(ActiveCurrencyAndAmount.class);
        final BranchAndFinancialInstitutionIdentification6 agent = Instancio.create(BranchAndFinancialInstitutionIdentification6.class);
        final PartyIdentification135 creditor = Instancio.create(PartyIdentification135.class);
        final CashAccount38 creditorAccount = Instancio.create(CashAccount38.class);
        final PartyIdentification135 debtor = Instancio.create(PartyIdentification135.class);
        final CashAccount38 debtorAccount = Instancio.create(CashAccount38.class);
        final RemittanceInformation16 remittanceInformation16 = Instancio.create(RemittanceInformation16.class);
        final DirectDebitTransaction10 directDebitTransaction10 = Instancio.create(DirectDebitTransaction10.class);

        final DirectDebitTransactionInformation24 expected = new DirectDebitTransactionInformation24()
                .paymentIdentification(paymentIdentification7)
                .paymentTypeInformation(paymentTypeInformation27)
                .interbankSettlementAmount(activeCurrencyAndAmount)
                .interbankSettlementDate(source.getInterbankSettlementDate())
                .chargeBearer(ChargeBearerType1Code.SLEV)
                .creditor(creditor)
                .creditorAccount(creditorAccount)
                .creditorAgent(agent)
                .debtor(debtor)
                .debtorAccount(debtorAccount)
                .debtorAgent(agent)
                .remittanceInformation(remittanceInformation16)
                .directDebitTransaction(directDebitTransaction10);

        given(bacPaymentIdentification7Mapper.map(source.getPaymentIdentification())).willReturn(paymentIdentification7);
        given(bacPaymentTypeInformation27Mapper.map(source.getPaymentTypeInformation())).willReturn(paymentTypeInformation27);
        given(bacActiveCurrencyAndAmountMapper.map(source.getInterbankSettlementAmount())).willReturn(activeCurrencyAndAmount);
        given(bacBranchAndFinancialInstitutionIdentification6Mapper.map("NOTPROVIDED")).willReturn(agent);
        given(bacPartyIdentification135Mapper.map(source.getCreditor())).willReturn(creditor);
        given(bacCashAccount38Mapper.map(source.getCreditorAccount())).willReturn(creditorAccount);
        given(bacPartyIdentification135Mapper.map(source.getDebtor())).willReturn(debtor);
        given(bacCashAccount38Mapper.map(source.getDebtorAccount())).willReturn(debtorAccount);
        given(bacRemittanceInformation16Mapper.map(source.getRemittanceInformation())).willReturn(remittanceInformation16);
        given(bacDirectDebitTransaction10Mapper.map(source.getMandateRelatedInformation())).willReturn(directDebitTransaction10);

        DirectDebitTransactionInformation24 result = mapper.map(source);

        assertThat(result).usingRecursiveComparison().isEqualTo(expected);

        then(bacPaymentIdentification7Mapper).should().map(source.getPaymentIdentification());
        then(bacPaymentTypeInformation27Mapper).should().map(source.getPaymentTypeInformation());
        then(bacActiveCurrencyAndAmountMapper).should().map(source.getInterbankSettlementAmount());
        then(bacBranchAndFinancialInstitutionIdentification6Mapper).should().map("NOTPROVIDED");
        then(bacPartyIdentification135Mapper).should().map(source.getCreditor());
        then(bacCashAccount38Mapper).should().map(source.getCreditorAccount());
        then(bacPartyIdentification135Mapper).should().map(source.getDebtor());
        then(bacCashAccount38Mapper).should().map(source.getDebtorAccount());
        then(bacRemittanceInformation16Mapper).should().map(source.getRemittanceInformation());
        then(bacDirectDebitTransaction10Mapper).should().map(source.getMandateRelatedInformation());
    }
}