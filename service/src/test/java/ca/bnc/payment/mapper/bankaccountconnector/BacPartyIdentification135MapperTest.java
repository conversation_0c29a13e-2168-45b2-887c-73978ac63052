package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditorIdentification;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.DebtorIdentification;
import ca.nbc.payment.etransfer.bankaccountconnector.model.Contact4;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PartyIdentification135;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BacPartyIdentification135MapperTest {

    private BacPartyIdentification135Mapper mapper;

    @Mock
    private BacContact4Mapper bacContact4Mapper;

    @BeforeEach
    void setUp() {
        mapper = new BacPartyIdentification135Mapper(bacContact4Mapper);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(bacContact4Mapper);
    }

    @Test
    void shouldMapCreditorIdentification() {
        CreditorIdentification creditor = Instancio.create(CreditorIdentification.class);
        Contact4 contact4 = Instancio.create(Contact4.class);

        given(bacContact4Mapper.map(creditor.getContactDetails())).willReturn(contact4);

        PartyIdentification135 result = mapper.map(creditor);

        assertThat(result.getName()).isEqualTo(creditor.getName());
        assertThat(result.getContactDetails()).isEqualTo(contact4);
        then(bacContact4Mapper).should().map(creditor.getContactDetails());
    }

    @Test
    void shouldMapDebtorIdentification() {
        DebtorIdentification debtor = Instancio.create(DebtorIdentification.class);
        Contact4 contact4 = Instancio.create(Contact4.class);

        given(bacContact4Mapper.map(debtor.getContactDetails())).willReturn(contact4);

        PartyIdentification135 result = mapper.map(debtor);

        assertThat(result.getName()).isEqualTo(debtor.getName());
        assertThat(result.getContactDetails()).isEqualTo(contact4);
        then(bacContact4Mapper).should().map(debtor.getContactDetails());
    }
}