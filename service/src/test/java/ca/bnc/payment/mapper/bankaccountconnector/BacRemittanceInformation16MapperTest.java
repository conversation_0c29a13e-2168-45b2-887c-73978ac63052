package ca.bnc.payment.mapper.bankaccountconnector;

import ca.nbc.payment.etransfer.bankaccountconnector.model.RemittanceInformation16;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class BacRemittanceInformation16MapperTest {
    private BacRemittanceInformation16Mapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new BacRemittanceInformation16Mapper();
    }

    @Test
    void shouldReturnNullWhenRemittanceInformationIsNull() {
        RemittanceInformation16 result = mapper.map(null);
        assertThat(result).isNull();
    }

    @Test
    void shouldMapRemittanceInformationToBankAccountConnectorEquivalent() {
        ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources
                .generated.model.RemittanceInformation16 remittanceInformation = Instancio.create(
                        ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources
                                .generated.model.RemittanceInformation16.class
        );

        RemittanceInformation16 result = mapper.map(remittanceInformation);

        assertThat(result.getUnstructured()).isEqualTo(remittanceInformation.getUnstructured());
    }
}