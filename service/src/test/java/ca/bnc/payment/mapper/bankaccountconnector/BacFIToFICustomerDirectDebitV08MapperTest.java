package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.FIToFICustomerCreditTransferV08;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DirectDebitTransactionInformation24;
import ca.nbc.payment.etransfer.bankaccountconnector.model.FIToFICustomerDirectDebitV08;
import ca.nbc.payment.etransfer.bankaccountconnector.model.GroupHeader94;
import ca.nbc.payment.etransfer.bankaccountconnector.model.HTTPHeader;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BacFIToFICustomerDirectDebitV08MapperTest {

    private BacFIToFICustomerDirectDebitV08Mapper mapper;

    @Mock
    private BacGroupHeader94Mapper bacGroupHeader94Mapper;
    @Mock
    private BacDirectDebitTransactionInformation24Mapper bacDirectDebitTransactionInformation24Mapper;

    @BeforeEach
    void setUp() {
        mapper = new BacFIToFICustomerDirectDebitV08Mapper(
                bacGroupHeader94Mapper,
                bacDirectDebitTransactionInformation24Mapper
        );
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(
                bacGroupHeader94Mapper,
                bacDirectDebitTransactionInformation24Mapper
        );
    }

    @Test
    void shouldMapAllFieldsCorrectly() {
        final String endToEndId = Instancio.create(String.class);
        final SendPaymentExecuteRequest request = Instancio.create(SendPaymentExecuteRequest.class);
        final PaymentSimulationHeader header = Instancio.create(PaymentSimulationHeader.class);
        final String requestId = header.xRequestId().toString();
        final FIToFICustomerCreditTransferV08 fiToFICustomerCreditTransfer = request.getFiToFICustomerCreditTransfer();

        final GroupHeader94 groupHeader94 = Instancio.create(GroupHeader94.class);
        final DirectDebitTransactionInformation24 directDebitTransactionInformation24 =
                Instancio.create(DirectDebitTransactionInformation24.class);
        final HTTPHeader httpHeader = Instancio.of(HTTPHeader.class)
                .set(field(HTTPHeader::getId), endToEndId)
                .set(field(HTTPHeader::getxC1ClientId), header.xClientId())
                .set(field(HTTPHeader::getxRequestId), requestId)
                .set(field(HTTPHeader::getxRetryIndicator), "false")
                .create();

        final FIToFICustomerDirectDebitV08 expected = new FIToFICustomerDirectDebitV08()
                .groupHeader(groupHeader94)
                .directDebitTransactionInformation(List.of(directDebitTransactionInformation24))
                .htTPHeader(httpHeader);

        given(bacGroupHeader94Mapper.map(fiToFICustomerCreditTransfer.getGroupHeader())).willReturn(groupHeader94);
        given(bacDirectDebitTransactionInformation24Mapper.map(fiToFICustomerCreditTransfer.getCreditTransferTransactionInformation()))
                .willReturn(directDebitTransactionInformation24);

        FIToFICustomerDirectDebitV08 result = mapper.map(endToEndId, header.xClientId(), requestId, request);

        assertThat(result).usingRecursiveComparison().isEqualTo(expected);

        verify(bacGroupHeader94Mapper).map(fiToFICustomerCreditTransfer.getGroupHeader());
        verify(bacDirectDebitTransactionInformation24Mapper).map(fiToFICustomerCreditTransfer.getCreditTransferTransactionInformation());
    }
}