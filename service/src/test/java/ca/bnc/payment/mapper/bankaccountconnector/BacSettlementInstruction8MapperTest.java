package ca.bnc.payment.mapper.bankaccountconnector;

import ca.nbc.payment.etransfer.bankaccountconnector.model.ClearingSystemIdentification3ChoiceProprietary;
import ca.nbc.payment.etransfer.bankaccountconnector.model.SettlementMethod2Code;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import static ca.bnc.payment.mapper.bankaccountconnector.BankAccountConnectorObjectTypes.PROPRIETARY;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class BacSettlementInstruction8MapperTest {

    private BacSettlementInstruction8Mapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new BacSettlementInstruction8Mapper();
    }

    @Test
    void shouldMapSettlementInstruction8() {
        var result = mapper.map();

        assertNotNull(result);
        assertEquals(SettlementMethod2Code.CLRG, result.getSettlementMethod());
        assertNotNull(result.getClearingSystem());
        assertInstanceOf(ClearingSystemIdentification3ChoiceProprietary.class, result.getClearingSystem());
        var proprietary = (ClearingSystemIdentification3ChoiceProprietary) result.getClearingSystem();
        assertEquals(PROPRIETARY, proprietary.getObjectType());
        assertEquals(ClearingSystemIdentification3ChoiceProprietary.ProprietaryEnum.ETR, proprietary.getProprietary());
    }
}
