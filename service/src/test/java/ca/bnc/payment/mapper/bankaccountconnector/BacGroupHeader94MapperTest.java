package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.GroupHeader93;
import ca.nbc.payment.etransfer.bankaccountconnector.model.BranchAndFinancialInstitutionIdentification6;
import ca.nbc.payment.etransfer.bankaccountconnector.model.GroupHeader94;
import ca.nbc.payment.etransfer.bankaccountconnector.model.SettlementInstruction8;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BacGroupHeader94MapperTest {

    private static final String DIRECT_PARTICIPANT_IDENTIFIER = "CA000006";
    private static final String INSTRUCTED_AGENT_MEMBER_IDENTIFICATION = "NOTPROVIDED";
    private static final String NUMBER_OF_TRANSACTIONS = "1";

    @Mock
    private BacSettlementInstruction8Mapper bacSettlementInstruction8Mapper;
    @Mock
    private BacBranchAndFinancialInstitutionIdentification6Mapper bacBranchAndFinancialInstitutionIdentification6Mapper;

    private BacGroupHeader94Mapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new BacGroupHeader94Mapper(
                bacSettlementInstruction8Mapper,
                bacBranchAndFinancialInstitutionIdentification6Mapper
        );
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(bacSettlementInstruction8Mapper, bacBranchAndFinancialInstitutionIdentification6Mapper);
    }

    @Test
    void shouldMapAllFieldsCorrectly() {
        GroupHeader93 groupHeader93 = Instancio.create(GroupHeader93.class);
        SettlementInstruction8 settlementInstruction = Instancio.create(SettlementInstruction8.class);
        BranchAndFinancialInstitutionIdentification6 instructingAgent = Instancio.create(BranchAndFinancialInstitutionIdentification6.class);
        BranchAndFinancialInstitutionIdentification6 instructedAgent = Instancio.create(BranchAndFinancialInstitutionIdentification6.class);

        final GroupHeader94 expected = new GroupHeader94()
                .messageIdentification(groupHeader93.getMessageIdentification())
                .creationDatetime(groupHeader93.getCreationDateTime())
                .numberOfTransactions(NUMBER_OF_TRANSACTIONS)
                .settlementInformation(settlementInstruction)
                .instructingAgent(instructingAgent)
                .instructedAgent(instructedAgent);

        given(bacSettlementInstruction8Mapper.map()).willReturn(settlementInstruction);
        given(bacBranchAndFinancialInstitutionIdentification6Mapper.map(DIRECT_PARTICIPANT_IDENTIFIER)).willReturn(instructingAgent);
        given(bacBranchAndFinancialInstitutionIdentification6Mapper.map(INSTRUCTED_AGENT_MEMBER_IDENTIFICATION)).willReturn(instructedAgent);

        GroupHeader94 result = mapper.map(groupHeader93);

        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
        then(bacSettlementInstruction8Mapper).should().map();
        then(bacBranchAndFinancialInstitutionIdentification6Mapper).should().map(DIRECT_PARTICIPANT_IDENTIFIER);
        then(bacBranchAndFinancialInstitutionIdentification6Mapper).should().map(INSTRUCTED_AGENT_MEMBER_IDENTIFICATION);
    }

    @Test
    void shouldHandleNullFieldsInGroupHeader93() {
        GroupHeader93 groupHeader93 = new GroupHeader93();
        SettlementInstruction8 settlementInstruction = Instancio.create(SettlementInstruction8.class);
        BranchAndFinancialInstitutionIdentification6 instructingAgent = Instancio.create(BranchAndFinancialInstitutionIdentification6.class);
        BranchAndFinancialInstitutionIdentification6 instructedAgent = Instancio.create(BranchAndFinancialInstitutionIdentification6.class);

        final GroupHeader94 expected = new GroupHeader94()
                .messageIdentification(groupHeader93.getMessageIdentification())
                .creationDatetime(groupHeader93.getCreationDateTime())
                .numberOfTransactions(NUMBER_OF_TRANSACTIONS)
                .settlementInformation(settlementInstruction)
                .instructingAgent(instructingAgent)
                .instructedAgent(instructedAgent);

        given(bacSettlementInstruction8Mapper.map()).willReturn(settlementInstruction);
        given(bacBranchAndFinancialInstitutionIdentification6Mapper.map(DIRECT_PARTICIPANT_IDENTIFIER)).willReturn(instructingAgent);
        given(bacBranchAndFinancialInstitutionIdentification6Mapper.map(INSTRUCTED_AGENT_MEMBER_IDENTIFICATION)).willReturn(instructedAgent);

        GroupHeader94 result = mapper.map(groupHeader93);

        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
        then(bacSettlementInstruction8Mapper).should().map();
        then(bacBranchAndFinancialInstitutionIdentification6Mapper).should().map(DIRECT_PARTICIPANT_IDENTIFIER);
        then(bacBranchAndFinancialInstitutionIdentification6Mapper).should().map(INSTRUCTED_AGENT_MEMBER_IDENTIFICATION);
    }
}