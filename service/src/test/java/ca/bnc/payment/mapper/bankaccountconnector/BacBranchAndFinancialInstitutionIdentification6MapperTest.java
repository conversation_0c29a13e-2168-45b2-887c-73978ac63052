package ca.bnc.payment.mapper.bankaccountconnector;

import ca.nbc.payment.etransfer.bankaccountconnector.model.BranchAndFinancialInstitutionIdentification6;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class BacBranchAndFinancialInstitutionIdentification6MapperTest {
    private BacBranchAndFinancialInstitutionIdentification6Mapper mapper;

    @BeforeEach
    void setup() {
        mapper = new BacBranchAndFinancialInstitutionIdentification6Mapper();
    }

    @Test
    void shouldMapBranchAndFinancialInstitutionIdentification6() {
        final String systemMemberIdentification = "systemMemberIdentification";

        BranchAndFinancialInstitutionIdentification6 result = mapper.map(systemMemberIdentification);
        assertThat(result).isNotNull()
                .satisfies(actual -> {
                    assertThat(actual.getFinancialInstitutionIdentification().getClearingSystemMemberIdentification()).isNotNull();
                    assertThat(actual.getFinancialInstitutionIdentification().getClearingSystemMemberIdentification().getMemberIdentification())
                            .isEqualTo(systemMemberIdentification);
                });
    }
}
