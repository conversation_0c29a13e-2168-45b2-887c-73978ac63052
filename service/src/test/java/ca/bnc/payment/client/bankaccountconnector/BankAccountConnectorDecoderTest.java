package ca.bnc.payment.client.bankaccountconnector;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.exception.RetryablePaymentSimulationException;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import feign.Request;
import feign.Response;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_BAD_REQUEST;
import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_INTERNAL_SERVER_ERROR;
import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_SERVICE_UNAVAILABLE;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BankAccountConnectorDecoderTest {
    private BankAccountConnectorDecoder bankAccountConnectorDecoder;

    @Mock
    private BankAccountConnectorDecoderErrorsBuilder errorBuilder;

    @BeforeEach
    void setUp() {
        bankAccountConnectorDecoder = new BankAccountConnectorDecoder(errorBuilder);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(errorBuilder);
    }

    private static Stream<Arguments> responseCodes() {
        final Request request = Instancio.create(Request.class);
        final Error badRequestError = Instancio.create(Error.class);
        final Error defaultError = Instancio.create(Error.class);

        return Stream.of(
                Arguments.of(
                        Response.builder().request(request).status(400).build(),
                        PaymentSimulationException.badRequest(
                                badRequestError.text("%s : %s".formatted(BANK_ACCOUNT_CONNECTOR_BAD_REQUEST, badRequestError.getText()))
                        ),
                        badRequestError
                ),
                Arguments.of(
                        Response.builder().request(request).status(500).build(),
                        PaymentSimulationException.internalServerError(
                                TECHNICAL_ERROR_CODE,
                                BANK_ACCOUNT_CONNECTOR_INTERNAL_SERVER_ERROR,
                                BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN,
                                NA
                        ),
                        Instancio.create(Error.class)
                ),
                Arguments.of(
                        Response.builder().request(request).status(503).build(),
                        new RetryablePaymentSimulationException(
                                TECHNICAL_ERROR_CODE,
                                BANK_ACCOUNT_CONNECTOR_SERVICE_UNAVAILABLE,
                                BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN,
                                NA
                        ),
                        Instancio.create(Error.class)
                ),
                Arguments.of(
                        Response.builder().request(request).status(418).build(),
                        PaymentSimulationException.internalServerError(defaultError),
                        defaultError
                ),
                Arguments.of(
                        Response.builder().request(request).status(404).build(),
                        PaymentSimulationException.internalServerError(defaultError),
                        defaultError
                ),
                Arguments.of(
                        Response.builder().request(request).status(422).build(),
                        PaymentSimulationException.internalServerError(defaultError),
                        defaultError
                )
        );
    }

    @ParameterizedTest(name = "responseCode = {0}, expectedException = {1}")
    @MethodSource("responseCodes")
    void shouldReturnExpectedExceptionBasedOnResponseStatus(final Response response,
                                                           final Exception expectedException,
                                                           final Error error) {

        when(errorBuilder.buildErrorFromResponseBody(response)).thenReturn(error);

        final Exception actual = bankAccountConnectorDecoder.decode(null, response);

        assertThat(actual)
                .isInstanceOf(expectedException.getClass())
                .usingRecursiveComparison()
                .isEqualTo(expectedException);
    }
}