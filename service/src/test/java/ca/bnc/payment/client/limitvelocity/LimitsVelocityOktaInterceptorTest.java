package ca.bnc.payment.client.limitvelocity;

import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class LimitsVelocityOktaInterceptorTest {

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_TOKEN = "Bearer jwtToken";
    private static final String LIMITS_VELOCITY_TOKEN_SCOPE = "limitsVelocityScope";

    private LimitsVelocityOktaInterceptor limitVelocityApiInterceptor;

    @Mock
    private OktaClientTokenManager oktaClientTokenManager;

    @Mock
    private RequestTemplate requestTemplate;

    @Mock
    private RequestInterceptor delegate;

    @BeforeEach
    void setUp() {
        limitVelocityApiInterceptor = new LimitsVelocityOktaInterceptor(oktaClientTokenManager);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(delegate,requestTemplate, oktaClientTokenManager);
    }

    @Test
    void shouldCreateCorrectDelegateWhenUsingOktaManager() {
        given(oktaClientTokenManager.getAccessToken(LIMITS_VELOCITY_TOKEN_SCOPE)).willReturn("jwtToken");
        limitVelocityApiInterceptor.apply(requestTemplate);
        then(requestTemplate).should().header(AUTHORIZATION_HEADER, BEARER_TOKEN);
    }


}