package ca.bnc.payment.client.bankaccountconnector;

import ca.bnc.payment.normalization.lib.service.NormalizationService;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ErrorModel;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Optional;
import java.util.stream.Stream;

import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_ERR_TABLE;
import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_INTERNAL_SERVER_ERROR;
import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BankAccountConnectorDecoderErrorsBuilderTest {

    private static final String ERROR_LOG = "An error occurred while parsing error response from bank account connector API";
    private static final ErrorModel BAC_API_ERROR_MODEL = Instancio.create(ErrorModel.class);

    private final Logger logger = LoggerFactory.getLogger(BankAccountConnectorDecoderErrorsBuilder.class.getName());
    private BankAccountConnectorDecoderErrorsBuilder bankAccountConnectorDecoderErrorsBuilder;

    @Mock
    private ObjectMapper objectMapper;
    @Mock
    private LoggingFacade loggingFacade;
    @Mock
    private Response response;
    @Mock
    private Response.Body body;
    @Mock
    private InputStream inputStream;
    @Mock
    private NormalizationService normalizationService;

    private static Stream<Arguments> normalizeCodesProvider() {
        return Stream.of(
                Arguments.argumentSet("Normalize Code not empty", Optional.of("ACCOUNT_TRANSIT_INVALID"), "ACCOUNT_TRANSIT_INVALID"),
                Arguments.argumentSet("Normal Code is empty", Optional.empty(), TECHNICAL_ERROR_CODE)
        );
    }

    @BeforeEach
    void setup() {
        bankAccountConnectorDecoderErrorsBuilder = new BankAccountConnectorDecoderErrorsBuilder(
                objectMapper, loggingFacade, normalizationService);
        given(response.body()).willReturn(body);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(loggingFacade, objectMapper, normalizationService);
    }

    @Test
    void shouldReturnDefaultErrorWhenResponseBodyIsNull() {
        given(response.body()).willReturn(null);
        Error actualError = bankAccountConnectorDecoderErrorsBuilder.buildErrorFromResponseBody(response);
        assertThat(actualError).isEqualTo(createDefaultError());
    }

    @Test
    void shouldReturnDefaultErrorAndLogErrorWhenInputStreamThrowsIOException() throws IOException {
        IOException ioException = Instancio.create(IOException.class);
        given(body.asInputStream()).willThrow(ioException);
        Error actualError = bankAccountConnectorDecoderErrorsBuilder.buildErrorFromResponseBody(response);
        assertThat(actualError).isEqualTo(createDefaultError());
        then(loggingFacade).should().error(logger, ERROR_LOG, ioException);
    }

    @Test
    void shouldReturnDefaultErrorAndLogErrorWhenObjectMapperThrowsException() throws IOException {
        RuntimeException exception = Instancio.create(RuntimeException.class);
        given(body.asInputStream()).willReturn(inputStream);
        given(objectMapper.readValue(inputStream, ErrorModel.class)).willThrow(exception);
        Error actualError = bankAccountConnectorDecoderErrorsBuilder.buildErrorFromResponseBody(response);
        assertThat(actualError).isEqualTo(createDefaultError());
        then(loggingFacade).should().error(logger, ERROR_LOG, exception);
        then(inputStream).should().close();
    }

    @ParameterizedTest
    @MethodSource("normalizeCodesProvider")
    void shouldReturnErrorWithNormalizedCodeWhenBuildingFromResponseBody(Optional<String> normalizeResult, String expectedErrorCode) throws IOException {
        given(body.asInputStream()).willReturn(inputStream);
        given(objectMapper.readValue(inputStream, ErrorModel.class)).willReturn(BAC_API_ERROR_MODEL);
        given(normalizationService.normalize(BANK_ACCOUNT_CONNECTOR_ERR_TABLE, BAC_API_ERROR_MODEL.getCode())).willReturn(normalizeResult);

        Error expectedError = createErrorWithCode(expectedErrorCode, BAC_API_ERROR_MODEL.getText());
        Error actualError = bankAccountConnectorDecoderErrorsBuilder.buildErrorFromResponseBody(response);

        assertThat(actualError).isEqualTo(expectedError);
        then(inputStream).should().close();
    }

    private Error createDefaultError() {
        return createErrorWithCode(TECHNICAL_ERROR_CODE, BANK_ACCOUNT_CONNECTOR_INTERNAL_SERVER_ERROR);
    }

    private Error createErrorWithCode(String code, String text) {
        return new Error()
                .code(code)
                .text(text)
                .origin(BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN)
                .rule(NA);
    }
}