package ca.bnc.payment.client.paymentregistration;

import ca.bnc.payment.client.common.ErrorBuilder;
import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.exception.RetryablePaymentSimulationException;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import feign.Response;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatusCode;

import java.util.stream.Stream;

import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.PAYMENT_REGISTRATION_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.REGISTRATION_API_UNAVAILABLE_LOG;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.http.HttpStatus.BAD_REQUEST;
import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;
import static org.springframework.http.HttpStatus.NOT_FOUND;
import static org.springframework.http.HttpStatus.SERVICE_UNAVAILABLE;

@ExtendWith(MockitoExtension.class)
class PaymentRegistrationDecoderTest {

    public static final String PAYMENT_REGISTRATION_API = "Payment Registration API";
    private PaymentRegistrationDecoder paymentRegistrationDecoder;

    @Mock
    private ErrorBuilder errorBuilder;

    @BeforeEach
    void setup() {
        paymentRegistrationDecoder = new PaymentRegistrationDecoder(errorBuilder);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(errorBuilder);
    }

    static Stream<Arguments> provideHttpStatusAndExpectedException() {
        return Stream.of(
                Arguments.argumentSet("when status is bad request", BAD_REQUEST),
                Arguments.argumentSet("when status is not found", NOT_FOUND),
                Arguments.argumentSet("when status is default case", INTERNAL_SERVER_ERROR)
        );
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("provideHttpStatusAndExpectedException")
    void shouldThrowCorrectException_whenHttpStatusMatches(HttpStatusCode httpStatus) {
        final Response response = Instancio.of(Response.class)
                .set(field(Response::status), httpStatus.value())
                .create();
        final Error error = Instancio.create(Error.class);
        when(errorBuilder.buildErrorFromResponseBody(response, PAYMENT_REGISTRATION_API)).thenReturn(error);

        final Exception exception = paymentRegistrationDecoder.decode(null, response);
        assertThat(exception).isInstanceOfSatisfying(
                PaymentSimulationException.class,
                e -> assertThat(e.getErrorInfo().status()).isEqualTo(httpStatus));
    }

    @Test
    void shouldThrowSimulationRetryableException() {
        final Response response = Instancio.of(Response.class)
                .set(field(Response::status), SERVICE_UNAVAILABLE.value())
                .create();
        final Error error = Instancio.of(Error.class)
                .set(field("code"), TECHNICAL_ERROR_CODE)
                .set(field("text"), REGISTRATION_API_UNAVAILABLE_LOG)
                .set(field("origin"), PAYMENT_REGISTRATION_SERVICE_ORIGIN)
                .set(field("rule"), NA)
                .create();
        when(errorBuilder.buildErrorFromResponseBody(response, PAYMENT_REGISTRATION_API)).thenReturn(error);

        final Exception actual = paymentRegistrationDecoder.decode(null, response);
        assertThat(actual).isInstanceOfSatisfying(
                RetryablePaymentSimulationException.class,
                e -> assertThat(e.getErrorInfo().error()).isEqualTo(error));
    }
}