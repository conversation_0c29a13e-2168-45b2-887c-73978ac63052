package ca.bnc.payment.client.common;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Errors;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class ErrorBuilderTest {

    private static final String API_NAME = "Test API";
    private static final String ERROR_LOG = "An error occurred while parsing error response from the " + API_NAME;
    private static final Logger LOGGER = LoggerFactory.getLogger(ErrorBuilder.class.getName());

    private ErrorBuilder errorBuilder;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private LoggingFacade loggingFacade;

    @Mock
    private Response.Body body;


    @BeforeEach
    void setUp() {
        errorBuilder = new ErrorBuilder(objectMapper, loggingFacade);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(objectMapper, loggingFacade, body);
    }

    @Test
    void shouldReturnNullWhenResponseBodyIsNull() {

        final Response response = Instancio.of(Response.class)
                .set(field(Response::body), null)
                .create();

        final Error error = errorBuilder.buildErrorFromResponseBody(response, API_NAME);

        assertThat(error).isNull();
    }

    @Test
    void shouldThrowExceptionWhenStreamingBody() throws Exception {
        final Response response = Instancio.of(Response.class)
                .set(field(Response::body), body)
                .create();

        final IOException ioException = Instancio.create(IOException.class);
        given(response.body().asInputStream()).willThrow(ioException);

        final Error error = errorBuilder.buildErrorFromResponseBody(response, API_NAME);

        assertThat(error).isNull();
        then(loggingFacade).should().error(LOGGER, ERROR_LOG, ioException);
    }

    @Test
    void shouldReturnExceptionWhenReadValueFromObjectMapper() throws Exception {
        final Response response = Instancio.of(Response.class)
                .set(field(Response::body), body)
                .create();
        final IOException ioException = Instancio.create(IOException.class);
        final InputStream inputStream = Instancio.create(ByteArrayInputStream.class);
        given(response.body().asInputStream()).willReturn(inputStream);
        given(objectMapper.readValue(response.body().asInputStream(), Errors.class)).willThrow(ioException);

        final Error error = errorBuilder.buildErrorFromResponseBody(response, API_NAME);

        assertThat(error).isNull();
        then(loggingFacade).should().error(LOGGER, ERROR_LOG, ioException);
    }

    @Test
    @DisplayName("should map error response")
    void shouldMapErrorResponse() throws Exception {
        final Response response = Instancio.of(Response.class)
                .set(field(Response::body), body)
                .create();
        Error expecteEerror = Instancio.of(Error.class)
                .set(field("code"), "ERR001")
                .set(field("text"), "Some error")
                .set(field("origin"), "SYSTEM")
                .set(field("rule"), "RULE1")
                .create();

        final Errors errors = Instancio.of(Errors.class)
                .set(field("errors"), List.of(expecteEerror))
                .create();
        final InputStream inputStream = Instancio.create(ByteArrayInputStream.class);
        given(response.body().asInputStream()).willReturn(inputStream);
        given(objectMapper.readValue(response.body().asInputStream(), Errors.class)).willReturn(errors);

        final Error error = errorBuilder.buildErrorFromResponseBody(response, API_NAME);

        assertThat(error).isNotNull()
                .usingRecursiveComparison()
                .isEqualTo(errors.getErrors().get(0));
    }

}