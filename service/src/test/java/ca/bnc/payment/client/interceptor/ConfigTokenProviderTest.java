package ca.bnc.payment.client.interceptor;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import ca.nbc.payment.pmt_security_library.utils.OktaUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;
import static ca.bnc.payment.utils.ErrorConstants.TOKEN_GENERATION_FAILED_LOG;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class ConfigTokenProviderTest {

    private static final String TOKEN_CONFIG_NAME = "testConfig";
    private static final String VALID_TOKEN = "valid-token";

    @Mock
    private OktaClientTokenManager oktaClientTokenManager;

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(oktaClientTokenManager);
    }

    @Test
    void whenTokenAvailable_thenReturnToken() {
        try (MockedStatic<OktaUtil> oktaUtil = mockStatic(OktaUtil.class)) {
            oktaUtil.when(() -> OktaUtil.getOktaToken(oktaClientTokenManager, TOKEN_CONFIG_NAME))
                    .thenReturn(VALID_TOKEN);

            final ConfigTokenProvider tokenProvider = new ConfigTokenProvider(oktaClientTokenManager, TOKEN_CONFIG_NAME);
            final String actual = tokenProvider.getToken();

            assertThat(actual).isEqualTo(VALID_TOKEN);
        }
    }

    @Test
    void whenTokenNotAvailable_thenThrowOktaException() {
        final PaymentSimulationException expected = PaymentSimulationException.internalServerError(
                TECHNICAL_ERROR_CODE,
                TOKEN_GENERATION_FAILED_LOG,
                SERVICE_ORIGIN,
                NA);
        try (MockedStatic<OktaUtil> oktaUtil = mockStatic(OktaUtil.class)) {
            oktaUtil.when(() -> OktaUtil.getOktaToken(oktaClientTokenManager, TOKEN_CONFIG_NAME))
                    .thenReturn(null);

            final ConfigTokenProvider tokenProvider = new ConfigTokenProvider(oktaClientTokenManager, TOKEN_CONFIG_NAME);

            assertThatThrownBy(tokenProvider::getToken)
                    .isInstanceOfSatisfying(PaymentSimulationException.class, e -> assertThat(e).usingRecursiveComparison().isEqualTo(expected));
        }
    }
}