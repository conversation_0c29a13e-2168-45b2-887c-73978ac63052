package ca.bnc.payment.client.moneyrequest;

import ca.bnc.payment.client.common.ErrorBuilder;
import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import feign.Response;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.http.HttpStatus.BAD_REQUEST;
import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;

@ExtendWith(MockitoExtension.class)
class MoneyRequestDecoderTest {

    private static final String MONEY_REQUEST_API_NAME = "Money Request API";

    private MoneyRequestDecoder moneyRequestDecoder;

    @Mock
    private ErrorBuilder errorBuilder;

    @BeforeEach
    void setup() {
        moneyRequestDecoder = new MoneyRequestDecoder(errorBuilder);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(errorBuilder);
    }

    static Stream<Arguments> provideHttpStatus() {
        return Stream.of(
                Arguments.argumentSet("when status is bad request expect bad request", BAD_REQUEST),
                Arguments.argumentSet("when status is other than bad request expect server error", INTERNAL_SERVER_ERROR)
        );
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("provideHttpStatus")
    void shouldThrowPaymentSimulationException(HttpStatus httpStatus) {
        Response response = mock(Response.class);
        when(response.status()).thenReturn(httpStatus.value());
        ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error error = Instancio.create(Error.class);
        when(errorBuilder.buildErrorFromResponseBody(response, MONEY_REQUEST_API_NAME)).thenReturn(error);

        final Exception exception = moneyRequestDecoder.decode(null, response);
        assertThat(exception)
                .isInstanceOfSatisfying(PaymentSimulationException.class,
                        e -> assertThat(e.getErrorInfo().status())
                                .isEqualTo(httpStatus));
    }

}
