package ca.bnc.payment.controller;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Errors;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MissingRequestHeaderException;

import static ca.bnc.payment.utils.ErrorConstants.GENERIC_ERROR_LOG;
import static ca.bnc.payment.utils.ErrorConstants.INTERNAL_SERVER_ERROR_GENERIC_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.REQUEST_INVALID_CODE;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.springframework.http.HttpStatus.BAD_REQUEST;
import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;

@ExtendWith(MockitoExtension.class)
class GlobalErrorHandlerTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(GlobalErrorHandler.class.getName());
    private static final String EXCEPTION_MESSAGE = "EXCEPTION_MESSAGE";

    private GlobalErrorHandler globalErrorHandler;

    @Mock
    private LoggingFacade loggingFacade;

    @BeforeEach
    void setUp() {
        globalErrorHandler = new GlobalErrorHandler(loggingFacade);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(loggingFacade);
    }


    @Test
    void shouldHandleMissingRequestHeaderException() {
        Error expectedError = new Error().code(REQUEST_INVALID_CODE).text(EXCEPTION_MESSAGE).origin(SERVICE_ORIGIN).rule(NA);
        MissingRequestHeaderException missingRequestHeaderException = mock(MissingRequestHeaderException.class);
        given(missingRequestHeaderException.getMessage()).willReturn(EXCEPTION_MESSAGE);

        ResponseEntity<Errors> actualResponse = globalErrorHandler.handleMissingRequestHeaderException(missingRequestHeaderException);

        assertThat(actualResponse)
                .satisfies(response -> {
                    assertThat(response.getStatusCode()).isEqualTo(BAD_REQUEST);
                    assertThat(response.getBody()).isInstanceOfSatisfying(
                            Errors.class,
                            errors -> assertThat(errors.getErrors())
                                    .hasSize(1)
                                    .first()
                                    .usingRecursiveComparison()
                                    .isEqualTo(expectedError)
                    );
                });

        then(loggingFacade).should().error(
                LOGGER,
                String.format(GENERIC_ERROR_LOG, REQUEST_INVALID_CODE, EXCEPTION_MESSAGE, SERVICE_ORIGIN, NA),
                missingRequestHeaderException);

    }

    @Test
    void shouldHandleProcessingSimulationException() {
        final PaymentSimulationException badRequest = PaymentSimulationException.badRequest(
                "ERROR_CODE",
                "Error message",
                SERVICE_ORIGIN,
                NA);

        final ResponseEntity<Errors> actual = globalErrorHandler.handleProcessingSimulationException(badRequest);

        assertThat(actual).satisfies(response -> {

            assertThat(response.getStatusCode()).isEqualTo(BAD_REQUEST);
            assertThat(response.getBody()).isInstanceOfSatisfying(
                    Errors.class,
                    errors -> assertThat(errors.getErrors())
                            .singleElement()
                            .usingRecursiveComparison()
                            .isEqualTo(badRequest.getErrorInfo().error())
            );
        });

        then(loggingFacade).should().error(
                LOGGER,
                GENERIC_ERROR_LOG.formatted(badRequest.getErrorInfo().error().getCode(),
                        badRequest.getErrorInfo().error().getText(),
                        SERVICE_ORIGIN,
                        NA),
                badRequest);
    }

    @Test
    void shouldThrowException() {
        final Error expectedError =
                new Error().code(TECHNICAL_ERROR_CODE).text(INTERNAL_SERVER_ERROR_GENERIC_LOG).origin(SERVICE_ORIGIN).rule(NA);
        final Exception exception = new Exception(EXCEPTION_MESSAGE);

        ResponseEntity<Errors> actualResponse = globalErrorHandler.handleException(exception);
        assertThat(actualResponse)
                .satisfies(response -> {
                    assertThat(response.getStatusCode()).isEqualTo(INTERNAL_SERVER_ERROR);
                    assertThat(response.getBody()).isInstanceOfSatisfying(
                            Errors.class,
                            errors -> assertThat(errors.getErrors())
                                    .singleElement()
                                    .usingRecursiveComparison()
                                    .isEqualTo(expectedError)
                    );
                });

        then(loggingFacade).should().error(
                LOGGER,
                String.format(GENERIC_ERROR_LOG, TECHNICAL_ERROR_CODE, INTERNAL_SERVER_ERROR_GENERIC_LOG, SERVICE_ORIGIN, NA),
                exception);
    }
}