package ca.bnc.payment.controller;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Map;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

class LogContextHelperTest {

    private static final String END_TO_END_IDENTIFICATION = Instancio.create(String.class);
    private static final UUID REQUEST_ID = Instancio.create(UUID.class);
    private static final String CLIENT_ID = Instancio.create(String.class);

    private LogContextHelper logContextHelper;

    @BeforeEach
    void setup() {
        logContextHelper = new LogContextHelper();
    }

    @Test
    void testContext() {
        Map<String, Object> actual = logContextHelper.contextFor(END_TO_END_IDENTIFICATION, REQUEST_ID, CLIENT_ID);
        assertThat(actual).containsExactlyInAnyOrderEntriesOf(Map.of(
                "endToEndBusinessIdentification", END_TO_END_IDENTIFICATION,
                "requestId", REQUEST_ID.toString(),
                "clientId", CLIENT_ID
        ));
    }

}
