package ca.bnc.payment.controller;

import ca.nbc.payment.lib.service.logging.LogContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class LogContextCleanerInterceptorTest {

    @Mock
    private LogContextHolder logContextHolder;
    @Mock
    private HttpServletRequest request;
    @Mock
    private HttpServletResponse response;
    @Mock
    private Object handler;
    @Mock
    private Exception exception;

    private LogContextCleanerInterceptor logContextCleanerInterceptor;

    @BeforeEach
    void setUp() {
        logContextCleanerInterceptor= new LogContextCleanerInterceptor(logContextHolder);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(
                logContextHolder,
                request,
                response,
                handler,
                exception);
    }


    @Test
    void testAfterCompletion() {
       logContextCleanerInterceptor.afterCompletion(request, response, handler, exception);
       then(logContextHolder).should().reset();
    }

}