package ca.bnc.payment.controller;

import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.bnc.payment.service.PaymentSimulationOrchestrator;
import ca.nbc.payment.lib.service.logging.LogContextHolder;
import org.instancio.Instancio;
import org.instancio.TypeToken;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.Map;
import java.util.UUID;
import java.util.function.Supplier;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith({MockitoExtension.class})
class PaymentProcessingSimulationControllerTest {

    private static final String END_TO_END_IDENTIFICATION = Instancio.create(String.class);
    private static final String X_CHANNEL_ID = Instancio.create(String.class);
    private static final ChannelType X_CHANNEL_TYPE = Instancio.create(ChannelType.class);
    private static final String X_CLIENT_ID = Instancio.create(String.class);
    private static final String ACCEPT = Instancio.create(String.class);
    private static final UUID X_REQUEST_ID = Instancio.create(UUID.class);
    private static final String TRACE_PARENT = Instancio.create(String.class);
    private static final String X_CLIENT_AGENT_ID = Instancio.create(String.class);
    private static final String X_AGENT_ID = Instancio.create(String.class);
    private static final String BNC_BUSINESS_TRACE_ID = Instancio.create(String.class);
    private static final String TRACE_STATE = Instancio.create(String.class);

    @Mock
    private PaymentSimulationOrchestrator paymentSimulationOrchestrator;

    @Mock
    private LogContextHolder logContextHolder;

    @Mock
    private LogContextHelper logContextHelper;

    @Captor
    private ArgumentCaptor<Supplier<ResponseEntity<Object>>> responseSupplierCaptor;

    private PaymentProcessingSimulationController paymentProcessingSimulationController;

    @BeforeEach
    public void setup() {
        paymentProcessingSimulationController = new PaymentProcessingSimulationController(
                paymentSimulationOrchestrator,
                logContextHolder,
                logContextHelper
        );
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(paymentSimulationOrchestrator, logContextHolder, logContextHelper);
    }

    @Test
    void whenSimulation_thenReturnAccepted() {
        final SendPaymentExecuteRequest paymentRequest = Instancio.create(SendPaymentExecuteRequest.class);
        final ResponseEntity<Object> expectedApiResponse = ResponseEntity.status(HttpStatus.ACCEPTED).body(Collections.emptyMap());
        final PaymentSimulationHeader paymentSimulationHeader = new PaymentSimulationHeader(TRACE_PARENT, X_REQUEST_ID,
                X_CHANNEL_ID, X_CHANNEL_TYPE, X_CLIENT_ID, BNC_BUSINESS_TRACE_ID, TRACE_STATE);
        final Map<String, Object> logContext = Instancio.create(new TypeToken<>() {
        });
        given(logContextHelper.contextFor(END_TO_END_IDENTIFICATION, X_REQUEST_ID, X_CLIENT_ID)).willReturn(logContext);
        given(logContextHolder.runWithContextNoReset(responseSupplierCaptor.capture(), eq(logContext))).willReturn(expectedApiResponse);

        final ResponseEntity<Object> actualApiResponse = paymentProcessingSimulationController.simulateDomesticOutgoingPayment(
                END_TO_END_IDENTIFICATION,
                X_CHANNEL_ID,
                X_CHANNEL_TYPE,
                X_CLIENT_ID,
                ACCEPT,
                X_REQUEST_ID,
                TRACE_PARENT,
                BNC_BUSINESS_TRACE_ID,
                paymentRequest,
                TRACE_STATE,
                X_CLIENT_AGENT_ID,
                X_AGENT_ID
        );

        assertThat(actualApiResponse).
                satisfies(apiResponse -> {
                    assertThat(apiResponse.getStatusCode()).isEqualTo(HttpStatus.ACCEPTED);
                    assertThat(apiResponse.getBody()).isEqualTo(Collections.emptyMap());
                });

        assertThat(responseSupplierCaptor.getAllValues())
                .singleElement()
                .satisfies(responseEntitySupplier -> assertThat(responseEntitySupplier.get())
                        .satisfies(supplierEntity -> {
                            assertThat(supplierEntity.getStatusCode()).isEqualTo(HttpStatus.ACCEPTED);
                            assertThat(supplierEntity.getBody()).isEqualTo(Collections.emptyMap());
                        }));
        then(paymentSimulationOrchestrator).should()
                .simulatePayment(paymentRequest,
                        END_TO_END_IDENTIFICATION,
                        paymentSimulationHeader

                );
    }

}
