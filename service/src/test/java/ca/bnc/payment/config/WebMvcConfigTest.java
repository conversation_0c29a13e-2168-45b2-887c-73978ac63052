package ca.bnc.payment.config;

import ca.bnc.payment.controller.LogContextCleanerInterceptor;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class WebMvcConfigTest {

    @Mock
    private LogContextCleanerInterceptor logContextCleanerInterceptor;

    @Mock
    private InterceptorRegistry interceptorRegistry;

    private WebMvcConfig webMvcConfig;

    @BeforeEach
    void setUp() {
        webMvcConfig = new WebMvcConfig(logContextCleanerInterceptor);
    }

    @AfterEach
    void noMoreInteraction() {
        verifyNoMoreInteractions(
                logContextCleanerInterceptor,
                interceptorRegistry);
    }

    @Test
    void whenCallingAddInterceptors_thenAddInterceptor() {
        webMvcConfig.addInterceptors(interceptorRegistry);

        then(interceptorRegistry).should().addInterceptor(logContextCleanerInterceptor);
    }

}