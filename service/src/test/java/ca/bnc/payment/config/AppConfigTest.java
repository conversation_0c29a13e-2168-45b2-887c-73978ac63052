package ca.bnc.payment.config;

import ca.bnc.payment.normalization.lib.service.NormalizationService;
import ca.bnc.payment.normalization.lib.service.impl.NormalizationServiceImpl;
import org.junit.jupiter.api.Test;

import java.time.Clock;
import java.time.ZoneOffset;

import static org.assertj.core.api.Assertions.assertThat;

class AppConfigTest {

    private final AppConfig appConfig = new AppConfig();

    @Test
    void clockShouldReturnSystemUTCClock() {
        Clock clock = appConfig.clock();
        assertThat(clock).isNotNull();
        assertThat(clock.getZone()).isEqualTo(ZoneOffset.UTC);
    }

    @Test
    void shouldReturnNormalizationServiceImpl() {
        NormalizationService normalizationService = appConfig.normalizationService();
        assertThat(normalizationService).isInstanceOf(NormalizationServiceImpl.class);
    }

}