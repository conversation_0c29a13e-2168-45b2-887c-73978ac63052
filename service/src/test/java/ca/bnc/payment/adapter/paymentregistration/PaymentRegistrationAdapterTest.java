package ca.bnc.payment.adapter.paymentregistration;

import ca.bnc.payment.client.paymentregistration.PaymentRegistrationApiClient;
import ca.bnc.payment.exception.RetryablePaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_payment_registration_resources.generated.model.Registrations;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import feign.RetryableException;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.UUID;

import static ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_payment_registration_resources.generated.model.ChannelType.fromValue;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.PAYMENT_REGISTRATION_API_UNAVAILABLE_LOG;
import static ca.bnc.payment.utils.ErrorConstants.PAYMENT_REGISTRATION_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class PaymentRegistrationAdapterTest {

    private static final String X_CHANNEL_ID = Instancio.create(String.class);
    private static final String X_CLIENT_ID = Instancio.create(String.class);
    private static final UUID X_REQUEST_ID = Instancio.create(UUID.class);
    private static final String TRACE_PARENT = Instancio.create(String.class);
    private static final ChannelType X_CHANNEL_TYPE = ChannelType.WEB;
    private static final String ACCEPT_HEADER = "application/vnd.ca.bnc.pmt+json;version=v1";
    private static final String BNC_BUSINESS_TRACE_ID = Instancio.create(String.class);
    private static final String TRACE_STATE = Instancio.create(String.class);

    private PaymentRegistrationAdapter registrationReaderAdapter;

    @Mock
    private PaymentRegistrationApiClient paymentRegistrationApiClient;

    @BeforeEach
    void setup() {
        registrationReaderAdapter = new PaymentRegistrationAdapter(paymentRegistrationApiClient);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(paymentRegistrationApiClient);
    }

    @Test
    void getRegistrations() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationHeader.class, "traceparent"), TRACE_PARENT)
                .set(field(PaymentSimulationHeader.class, "xRequestId"), X_REQUEST_ID)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), X_CHANNEL_ID)
                .set(field(PaymentSimulationHeader.class, "xChannelType"), X_CHANNEL_TYPE)
                .set(field(PaymentSimulationHeader.class, "xClientId"), X_CLIENT_ID)
                .set(field(PaymentSimulationHeader.class, "bncBusinessTraceId"), BNC_BUSINESS_TRACE_ID)
                .set(field(PaymentSimulationHeader.class, "tracestate"), TRACE_STATE)
                .create();
        final ResponseEntity<Registrations> expectedResponse = ResponseEntity.ok(Instancio.create(Registrations.class));

        given(paymentRegistrationApiClient.getRegistrations(
                TRACE_PARENT,
                TRACE_STATE,
                BNC_BUSINESS_TRACE_ID,
                ACCEPT_HEADER,
                X_REQUEST_ID.toString(),
                X_CHANNEL_ID,
                fromValue(X_CHANNEL_TYPE.toString()),
                X_CLIENT_ID,
                null,
                null,
                null
        )).willReturn(expectedResponse);

        final ResponseEntity<Registrations> actualResponse = registrationReaderAdapter.getRegistrations(paymentSimulationRequestContext);

        assertThat(actualResponse).isEqualTo(expectedResponse);
    }

    @Test
    void shouldThrowRetryableException() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationHeader.class, "traceparent"), TRACE_PARENT)
                .set(field(PaymentSimulationHeader.class, "xRequestId"), X_REQUEST_ID)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), X_CHANNEL_ID)
                .set(field(PaymentSimulationHeader.class, "xChannelType"), X_CHANNEL_TYPE)
                .set(field(PaymentSimulationHeader.class, "xClientId"), X_CLIENT_ID)
                .set(field(PaymentSimulationHeader.class, "bncBusinessTraceId"), BNC_BUSINESS_TRACE_ID)
                .set(field(PaymentSimulationHeader.class, "tracestate"), TRACE_STATE)
                .create();
        final RetryableException timeoutException = Instancio.create(RetryableException.class);

        final Error expectedError = new Error()
                .code(TECHNICAL_ERROR_CODE)
                .text(PAYMENT_REGISTRATION_API_UNAVAILABLE_LOG)
                .origin(PAYMENT_REGISTRATION_SERVICE_ORIGIN)
                .rule(NA);

        given(paymentRegistrationApiClient.getRegistrations(
                TRACE_PARENT,
                TRACE_STATE,
                BNC_BUSINESS_TRACE_ID,
                ACCEPT_HEADER,
                X_REQUEST_ID.toString(),
                X_CHANNEL_ID,
                fromValue(X_CHANNEL_TYPE.toString()),
                X_CLIENT_ID,
                null,
                null,
                null
        )).willThrow(timeoutException);

        assertThatThrownBy(() -> registrationReaderAdapter.getRegistrations(paymentSimulationRequestContext))
                .isInstanceOfSatisfying(
                        RetryablePaymentSimulationException.class,
                        e -> assertThat(e.getErrorInfo().error())
                                .usingRecursiveComparison()
                                .isEqualTo(expectedError));

    }
}