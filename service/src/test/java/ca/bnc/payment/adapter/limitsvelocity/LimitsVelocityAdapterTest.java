package ca.bnc.payment.adapter.limitsvelocity;

import ca.bnc.payment.client.limitvelocity.LimitsVelocityApiClient;
import ca.bnc.payment.exception.RetryablePaymentSimulationException;
import ca.bnc.payment.mapper.limitsvelocity.LimitsVelocityRequestMapper;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.model.VelocitiesSimulation;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import feign.RetryableException;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static ca.bnc.payment.utils.ErrorConstants.LIMITS_VELOCITY_API_UNAVAILABLE_LOG;
import static ca.bnc.payment.utils.ErrorConstants.LIMITS_VELOCITY_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class LimitsVelocityAdapterTest {

    private static final String ACCEPT_VERSION_HEADER = "v1";

    private LimitsVelocityAdapter limitsVelocityAdapter;

    @Mock
    private LimitsVelocityApiClient limitsVelocityApiClient;

    @Mock
    private LimitsVelocityRequestMapper limitsVelocityRequestMapper;

    @BeforeEach
    void setup() {
        limitsVelocityAdapter = new LimitsVelocityAdapter(limitsVelocityApiClient, limitsVelocityRequestMapper);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(limitsVelocityApiClient, limitsVelocityRequestMapper);
    }

    @Test
    void shouldSimulateLimitsVelocities() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.create(PaymentSimulationRequestContext.class);
        VelocitiesSimulation velocitiesSimulation = Instancio.create(VelocitiesSimulation.class);
        given(limitsVelocityRequestMapper.mapVelocitiesSimulation(paymentSimulationRequestContext)).willReturn(velocitiesSimulation);

        assertThatCode(() -> limitsVelocityAdapter.simulateVelocities(paymentSimulationRequestContext)).doesNotThrowAnyException();

        then(limitsVelocityRequestMapper).should().mapVelocitiesSimulation(paymentSimulationRequestContext);
        then(limitsVelocityApiClient).should().simulationVelocities(
                paymentSimulationRequestContext.paymentSimulationHeader().xChannelId(),
                ACCEPT_VERSION_HEADER,
                paymentSimulationRequestContext.paymentSimulationHeader().xRequestId().toString(),
                velocitiesSimulation);
    }

    @Test
    void shouldThrowServerException() {
        PaymentSimulationRequestContext paymentSimulationRequestContext = Instancio.create(PaymentSimulationRequestContext.class);
        VelocitiesSimulation velocitiesSimulation = Instancio.create(VelocitiesSimulation.class);

        final RetryablePaymentSimulationException expected =
                new RetryablePaymentSimulationException(
                        TECHNICAL_ERROR_CODE,LIMITS_VELOCITY_API_UNAVAILABLE_LOG,LIMITS_VELOCITY_SERVICE_ORIGIN,NA);
        RetryableException timeoutException = Instancio.create(RetryableException.class);
        given(limitsVelocityRequestMapper.mapVelocitiesSimulation(paymentSimulationRequestContext))
                .willReturn(velocitiesSimulation);
        given(limitsVelocityApiClient.simulationVelocities(
                paymentSimulationRequestContext.paymentSimulationHeader().xChannelId(),
                ACCEPT_VERSION_HEADER,
                paymentSimulationRequestContext.paymentSimulationHeader().xRequestId().toString(),
                velocitiesSimulation)).willThrow(timeoutException);

        assertThatThrownBy(() -> limitsVelocityAdapter.simulateVelocities(paymentSimulationRequestContext))
                        .usingRecursiveComparison().isEqualTo(expected);

        then(limitsVelocityRequestMapper).should().mapVelocitiesSimulation(paymentSimulationRequestContext);
        then(limitsVelocityApiClient).should().simulationVelocities(
                paymentSimulationRequestContext.paymentSimulationHeader().xChannelId(),
                ACCEPT_VERSION_HEADER,
                paymentSimulationRequestContext.paymentSimulationHeader().xRequestId().toString(),
                velocitiesSimulation);
    }
}