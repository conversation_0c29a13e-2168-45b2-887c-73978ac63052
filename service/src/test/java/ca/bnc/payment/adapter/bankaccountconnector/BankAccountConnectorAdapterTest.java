package ca.bnc.payment.adapter.bankaccountconnector;

import ca.bnc.payment.client.bankaccountconnector.BankAccountConnectorApiClient;
import ca.bnc.payment.exception.ErrorInfo;
import ca.bnc.payment.exception.RetryablePaymentSimulationException;
import ca.bnc.payment.mapper.bankaccountconnector.DebitRequestMapper;
import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.Action;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ChannelType;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DebitRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DebitResponse;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentType;
import feign.RetryableException;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.UUID;

import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_TIMEOUT;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BankAccountConnectorAdapterTest {

    private static final String RETRY_INDICATOR = "false";
    private static final String END_TO_END_ID = Instancio.create(String.class);
    private static final ChannelType X_CHANNEL_TYPE = ChannelType.WEB;
    private static final PaymentType PAYMENT_TYPE = PaymentType.REGULAR_PAYMENT;

    private static final DebitRequest debitRequest = Instancio.create(DebitRequest.class);
    private static final PaymentSimulationHeader HEADER = Instancio.of(PaymentSimulationHeader.class)
            .set(field(PaymentSimulationHeader::bncBusinessTraceId), Instancio.create(UUID.class).toString())
            .create();

    private BankAccountConnectorAdapter bankAccountConnectorAdapter;

    @Mock
    private DebitRequestMapper debitRequestMapper;

    @Mock
    private BankAccountConnectorApiClient bankAccountConnectorApiClient;

    @BeforeEach
    void setUp() {
        bankAccountConnectorAdapter = new BankAccountConnectorAdapter(
                debitRequestMapper,
                bankAccountConnectorApiClient
        );
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(debitRequestMapper, bankAccountConnectorApiClient);
    }

    private PaymentSimulationRequestContext createContext() {
        return Instancio.of(PaymentSimulationRequestContext.class)
                .set(field(PaymentSimulationRequestContext::paymentSimulationHeader), HEADER)
                .set(field(PaymentSimulationRequestContext::paymentRequest), Instancio.create(SendPaymentExecuteRequest.class))
                .create();
    }

    @Test
    void shouldSuccessfullyInitiateDebit() {
        PaymentSimulationRequestContext context = createContext();
        DebitResponse debitResponse = Instancio.create(DebitResponse.class);
        ResponseEntity<DebitResponse> expected = ResponseEntity.ok(debitResponse);

        given(debitRequestMapper.toDebitRequest(END_TO_END_ID, HEADER.xClientId(),
                HEADER.xRequestId().toString(),
                context.paymentRequest())).willReturn(debitRequest);
        given(bankAccountConnectorApiClient.initiateDebit(
                HEADER.xChannelId(), X_CHANNEL_TYPE, Action.SEND, PAYMENT_TYPE,
                HEADER.xClientId(), RETRY_INDICATOR, END_TO_END_ID,
                HEADER.xRequestId().toString(), UUID.fromString(HEADER.bncBusinessTraceId()),
                HEADER.traceparent(), HEADER.tracestate(), debitRequest
        )).willReturn(expected);

        ResponseEntity<DebitResponse> actualResponse = bankAccountConnectorAdapter.initiateDebit(
                END_TO_END_ID, context, X_CHANNEL_TYPE, PAYMENT_TYPE);

        assertThat(actualResponse).isEqualTo(expected);
    }

    @Test
    void shouldThrowRetryableExceptionWhenApiClientThrowsException() {
        PaymentSimulationRequestContext context = createContext();
        RetryableException timeoutException = Instancio.create(RetryableException.class);


        final ErrorInfo expectedErrorInfo = ErrorInfo.internalServerError(
                new Error()
                        .code(TECHNICAL_ERROR_CODE)
                        .text(BANK_ACCOUNT_CONNECTOR_TIMEOUT)
                        .origin(BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN)
                        .rule(NA)
        );

        given(debitRequestMapper.toDebitRequest(END_TO_END_ID, HEADER.xClientId(),
                String.valueOf(HEADER.xRequestId()), context.paymentRequest())).willReturn(debitRequest);
        given(bankAccountConnectorApiClient.initiateDebit(
                HEADER.xChannelId(),
                X_CHANNEL_TYPE,
                Action.SEND,
                PAYMENT_TYPE,
                HEADER.xClientId(),
                RETRY_INDICATOR,
                END_TO_END_ID,
                HEADER.xRequestId().toString(),
                UUID.fromString(HEADER.bncBusinessTraceId()),
                HEADER.traceparent(), HEADER.tracestate(), debitRequest))
                .willThrow(timeoutException);

        assertThatThrownBy(() -> bankAccountConnectorAdapter.initiateDebit(
                END_TO_END_ID, context, X_CHANNEL_TYPE, PAYMENT_TYPE))
                .isInstanceOfSatisfying(
                        RetryablePaymentSimulationException.class,
                        e -> assertThat(e.getErrorInfo())
                                .usingRecursiveComparison()
                                .isEqualTo(expectedErrorInfo));
    }
}
