package ca.bnc.payment.adapter.moneyrequest;

import ca.bnc.payment.client.moneyrequest.MoneyRequestApiClient;
import ca.bnc.payment.exception.RetryablePaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.model.ChannelType;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.model.DomesticFulfillmentMoneyRequest;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.FIToFICustomerCreditTransferV08;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SupplementaryData;
import feign.RetryableException;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.UUID;

import static ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.model.ChannelType.fromValue;
import static ca.bnc.payment.utils.ErrorConstants.MONEY_REQUEST_API_UNAVAILABLE_LOG;
import static ca.bnc.payment.utils.ErrorConstants.MONEY_REQUEST_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class MoneyRequestAdapterTest {

    private static final String X_CHANNEL_ID = Instancio.create(String.class);
    private static final String X_CLIENT_ID = Instancio.create(String.class);
    private static final UUID X_REQUEST_ID = Instancio.create(UUID.class);
    private static final String TRACE_PARENT = Instancio.create(String.class);
    private static final ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType X_CHANNEL_TYPE =
            ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType.WEB;
    private static final String ACCEPT_HEADER = "application/vnd.ca.bnc.pmt+json;version=v1";
    private static final String BNC_BUSINESS_TRACE_ID = "3bcdf7fa-7e48-4565-9751-d8acbdb64d8b";
    private static final String TRACE_STATE = Instancio.create(String.class);
    private static final String INTERAC_MONEY_REQUEST_ID = Instancio.create(String.class);

    private MoneyRequestAdapter moneyRequestAdapter;

    @Mock
    private MoneyRequestApiClient moneyRequestApiClient;

    @BeforeEach
    void setup() {
        moneyRequestAdapter = new MoneyRequestAdapter(moneyRequestApiClient);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(moneyRequestApiClient);
    }

    @Test
    void getIncomingMoneyRequest() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = createPaymentSimulationRequestContext();
        final ResponseEntity<DomesticFulfillmentMoneyRequest> expectedResponse =
                ResponseEntity.ok(Instancio.create(DomesticFulfillmentMoneyRequest.class));

        given(moneyRequestApiClient.getIncomingMoneyRequest(
                INTERAC_MONEY_REQUEST_ID,
                X_CHANNEL_ID,
                fromValue(X_CHANNEL_TYPE.getValue()),
                X_REQUEST_ID,
                X_CLIENT_ID,
                ACCEPT_HEADER,
                TRACE_PARENT,
                TRACE_STATE,
                UUID.fromString(BNC_BUSINESS_TRACE_ID),
                null,
                null
        )).willReturn(expectedResponse);

        final ResponseEntity<DomesticFulfillmentMoneyRequest> actualResponse =
                moneyRequestAdapter.getIncomingMoneyRequest(paymentSimulationRequestContext);

        assertThat(actualResponse).isEqualTo(expectedResponse);
    }

    @Test
    void shouldThrowRetryableException() {
        final PaymentSimulationRequestContext paymentSimulationRequestContext = createPaymentSimulationRequestContext();
        final RetryableException timeoutException = Instancio.create(RetryableException.class);

        given(moneyRequestApiClient.getIncomingMoneyRequest(
                INTERAC_MONEY_REQUEST_ID,
                X_CHANNEL_ID,
                fromValue(X_CHANNEL_TYPE.getValue()),
                X_REQUEST_ID,
                X_CLIENT_ID,
                ACCEPT_HEADER,
                TRACE_PARENT,
                TRACE_STATE,
                UUID.fromString(BNC_BUSINESS_TRACE_ID),
                null,
                null
        )).willThrow(timeoutException);

        assertThatThrownBy(() -> moneyRequestAdapter.getIncomingMoneyRequest(paymentSimulationRequestContext))
                .isInstanceOfSatisfying(
                        RetryablePaymentSimulationException.class,
                        e -> {
                            assertThat(e.getErrorInfo().error().getCode()).isEqualTo(TECHNICAL_ERROR_CODE);
                            assertThat(e.getMessage()).isEqualTo(MONEY_REQUEST_API_UNAVAILABLE_LOG);
                            assertThat(e.getErrorInfo().error().getOrigin()).isEqualTo(MONEY_REQUEST_SERVICE_ORIGIN);
                            assertThat(e.getErrorInfo().error().getRule()).isEqualTo(NA);
                        });
    }

    private PaymentSimulationRequestContext createPaymentSimulationRequestContext() {
        final PaymentSimulationHeader header = Instancio.of(PaymentSimulationHeader.class)
                .set(field(PaymentSimulationHeader.class, "traceparent"), TRACE_PARENT)
                .set(field(PaymentSimulationHeader.class, "xRequestId"), X_REQUEST_ID)
                .set(field(PaymentSimulationHeader.class, "xChannelId"), X_CHANNEL_ID)
                .set(field(PaymentSimulationHeader.class, "xChannelType"), X_CHANNEL_TYPE)
                .set(field(PaymentSimulationHeader.class, "xClientId"), X_CLIENT_ID)
                .set(field(PaymentSimulationHeader.class, "bncBusinessTraceId"), BNC_BUSINESS_TRACE_ID)
                .set(field(PaymentSimulationHeader.class, "tracestate"), TRACE_STATE)
                .create();

        final SupplementaryData supplementaryData = new SupplementaryData();
        supplementaryData.setInteracMoneyRequestId(INTERAC_MONEY_REQUEST_ID);

        final CreditTransferTransaction39 creditTransferInfo = new CreditTransferTransaction39();
        creditTransferInfo.setSupplementaryData(supplementaryData);

        final FIToFICustomerCreditTransferV08 fiToFICustomerCreditTransfer = new FIToFICustomerCreditTransferV08();
        fiToFICustomerCreditTransfer.setCreditTransferTransactionInformation(creditTransferInfo);

        final SendPaymentExecuteRequest request = new SendPaymentExecuteRequest();
        request.setFiToFICustomerCreditTransfer(fiToFICustomerCreditTransfer);

        return new PaymentSimulationRequestContext(request, header, "");
    }
}