package ca.bnc.payment.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

class ParticipantIdUtilTest {

    private static final String BNC_PARTICIPANT = "CA000006";
    private static final String OS_PARTICIPANT = "CA000612";
    private static final String UKNOWN_PARTICIPANT = "CA000007";

    private static Stream<Arguments> bncInputProvider() {
        return Stream.of(
                Arguments.of(BNC_PARTICIPANT, Boolean.TRUE),
                Arguments.of(OS_PARTICIPANT, Boolean.FALSE),
                Arguments.of(UKNOWN_PARTICIPANT, Boolean.FALSE)
        );
    }

    private static Stream<Arguments> osInputProvider() {
        return Stream.of(
                Arguments.of(OS_PARTICIPANT, Boolean.TRUE),
                Arguments.of(BNC_PARTICIPANT, Boolean.FALSE),
                Arguments.of(UKNOWN_PARTICIPANT, Boolean.FALSE)
        );
    }

    @Test
    void testGetIdentifier() {
        final ParticipantIdUtil participantIdUtil = new ParticipantIdUtil(BNC_PARTICIPANT);
        assertThat(participantIdUtil.getDirectParticipantIdentifier())
                .isEqualTo(BNC_PARTICIPANT);
    }

    @ParameterizedTest(name = "directParticipantId = {0}, isBncParticipant = {1}")
    @MethodSource("bncInputProvider")
    void testIsBncParticipant(final String directParticipantId, final boolean expectedResult) {

        final ParticipantIdUtil participantIdUtil = new ParticipantIdUtil(directParticipantId);
        assertThat(participantIdUtil.isBncParticipant()).isEqualTo(expectedResult);
    }

    @ParameterizedTest(name = "directParticipantId = {0}, isOsParticipant = {1}")
    @MethodSource("osInputProvider")
    void testIsOsParticipant(final String directParticipantId, final boolean expectedResult) {

        final ParticipantIdUtil participantIdUtil = new ParticipantIdUtil(directParticipantId);
        assertThat(participantIdUtil.isOsParticipant()).isEqualTo(expectedResult);
    }
}