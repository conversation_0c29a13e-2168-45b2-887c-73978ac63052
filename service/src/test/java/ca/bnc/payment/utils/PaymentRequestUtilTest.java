package ca.bnc.payment.utils;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice.ProprietaryEnum;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.MandateRelatedSupplementaryData;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;

class PaymentRequestUtilTest {
    private PaymentRequestUtil paymentRequestUtil;

    @BeforeEach
    void setUp() {
        paymentRequestUtil = new PaymentRequestUtil();
    }

    @Test
    void shouldReturnProprietaryEnum() {
        SendPaymentExecuteRequest sendPaymentExecuteRequest = Instancio.of(SendPaymentExecuteRequest.class)
                .set(field(LocalInstrument2Choice.class, "proprietary"),
                        ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT)
                .create();

        ProprietaryEnum result = paymentRequestUtil.getProprietary(sendPaymentExecuteRequest);

        assertThat(result).isNotNull();
    }

    @Test
    void shouldReturnOriginalChannelType() {
        SendPaymentExecuteRequest sendPaymentExecuteRequest = Instancio.of(SendPaymentExecuteRequest.class)
                .set(field(MandateRelatedSupplementaryData.class, "originalChannelType"),
                        MandateRelatedSupplementaryData.OriginalChannelTypeEnum.BATCH)
                .create();

        MandateRelatedSupplementaryData.OriginalChannelTypeEnum result =
                paymentRequestUtil.getOriginalChannelType(sendPaymentExecuteRequest);

        assertThat(result).isEqualTo(MandateRelatedSupplementaryData.OriginalChannelTypeEnum.BATCH);
    }

}