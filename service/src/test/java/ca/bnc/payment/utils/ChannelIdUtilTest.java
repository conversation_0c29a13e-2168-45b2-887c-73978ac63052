package ca.bnc.payment.utils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.assertj.core.api.Assertions.assertThat;

class ChannelIdUtilTest {

    private ChannelIdUtil channelIdUtil;

    @BeforeEach
    void setUp() {
        channelIdUtil = new ChannelIdUtil();
    }


    @ParameterizedTest(name = "{index} - channelId = {0} expected boolean = {1}")
    @CsvSource(value = {"2777,false", "2778, false", "5156, false", "6502, false", "8131, true", "5327, true", "OSFIN, false", "6176, false"})
    void isBneChannelId(String channelId, boolean expected) {
        assertThat(channelIdUtil.isBneChannelId(channelId)).isEqualTo(expected);
    }

    @ParameterizedTest(name = "{index} - channelId = {0} expected boolean = {1}")
    @CsvSource(value = {"6676,true", "0000,false"})
    void isDeferredChannelId(String channelId, boolean expected) {
        assertThat(channelIdUtil.isDeferredChannelId(channelId)).isEqualTo(expected);
    }
}