package ca.bnc.payment.utils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class IdGeneratorTest {

    private IdGenerator idGenerator;

    @BeforeEach
    void setUp() {
        idGenerator = new IdGenerator();
    }

    @Test
    void testGenerateInstructionIdentification() {
        assertThat(idGenerator.generateInstructionIdentification())
                .isNotEmpty()
                .hasSize(32);
    }

}
