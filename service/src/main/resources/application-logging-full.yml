logging:
  logDomain: PAYMENT_PROCESSING_SIMULATION
  config:
    api:
      ["PUT::/et_pmt_proc/etransfer-payment-processing-simulation/{endToEndBusinessIdentification}"]:
        provider:
          name: PAYMENT_PROCESSING_SIMULATION
          operation: PAYMENT_PROCESSING_SIMULATION_API
        request:
          allPathVariables: true
          allHeaders: true
          body: true
        response:
          allPathVariables: true
          allHeaders: true
          body: true
      ["PROVIDER:POST::/payments/{id}/debit"]:
        hostname: "${providers.bank-account-connector.url}"
        provider:
          name: BANK_ACCOUNT_CONNECTOR
          operation: INITIATE_DEBIT
        request:
          allPathVariables: true
          allHeaders: true
          body: true
        response:
          allPathVariables: true
          allHeaders: true
          body: true
      ["PROVIDER:POST::/velocities/simulation"]:
        hostname: "${providers.limits-velocity.url}"
        provider:
          name: LIMIT_VELOCITY
          operation: SIMULATE_VELOCITY
        request:
          allPathVariables: true
          allHeaders: true
          body: true
        response:
          allPathVariables: true
          allHeaders: true
          body: true
      ["PROVIDER:GET::/pmt_rail_net/registrations"]:
        hostname: "${providers.payment-registration.url}"
        provider:
          name: PAYMENT_REGISTRATION
          operation: GET_REGISTRATION
        request:
          allPathVariables: true
          allParameters: true
          allHeaders: true
          body: true
        response:
          allPathVariables: true
          allHeaders: true
          body: true
      [ "PROVIDER:GET::/et_pmt_proc/incoming-money-request/{interacMoneyRequestId}" ]:
        hostname: "${providers.money-request.url}"
        provider:
          name: MONEY_REQUEST
          operation: GET_MONEY_REQUEST
        request:
          allPathVariables: true
          allHeaders: true
          body: true
        response:
          allPathVariables: true
          allHeaders: true
          body: true
      ["PROVIDER:POST::/oauth2/{authorizationServerId}/v1/token"]:
        hostname: "${okta.jwks.url}"
        provider:
          name: OKTA_JWKS
          operation: GET_TOKEN
        request:
          body: true
          allHeaders: true
          allPathVariables: true
          allQueryVariables: true
        response:
          body: true
          allHeaders: true
          allPathVariables: true
          allQueryVariables: true