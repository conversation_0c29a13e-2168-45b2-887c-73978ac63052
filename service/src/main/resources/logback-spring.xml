<?xml version="1.0" encoding="UTF-8"?>

<configuration>

    <!-- Payment style properties -->
    <springProperty scope="context" name="application.name" source="spring.application.name"/>
    <springProperty scope="context" name="application.environment" source="spring.profiles.active"/>
    <springProperty scope="context" name="version" source="spring.application.version"/>

    <property name="LOG_LEVEL" value="info"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <sequence/>
                <timestamp>
                    <fieldName>timestamp</fieldName>
                    <timeZone>America/Montreal</timeZone>
                    <pattern>yyyy-MM-dd'T'HH:mm:ss.SSS</pattern>
                </timestamp>
                <pattern>
                    <pattern>
                        { "dd.trace_id": "%X{dd.trace_id:-0}", "dd.span_id": "%X{dd.span_id:-0}" }
                    </pattern>
                </pattern>
                <context/>
                <version>
                    <fieldName>[ignore]</fieldName>
                </version>
                <loggerName/>
                <ThreadName/>
                <logLevel/>
                <message/>
                <stackTrace>
                    <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                        <maxDepthPerThrowable>30</maxDepthPerThrowable>
                        <maxLength>8192</maxLength>
                        <shortenedClassNameLength>80</shortenedClassNameLength>
                        <!-- generated class names exclusions -->
                        <exclude>\$\$FastClassByCGLIB\$\$</exclude>
                        <exclude>\$\$EnhancerBySpringCGLIB\$\$</exclude>
                        <exclude>^sun\.reflect\..*\.invoke</exclude>
                        <!-- JDK internals exclusions -->
                        <exclude>^com\.sun\.</exclude>
                        <exclude>^sun\.net\.</exclude>
                        <exclude>^sun\.security\.ssl\.</exclude>
                        <!-- dynamic invocation exclusions -->
                        <exclude>^net\.sf\.cglib\.proxy\.MethodProxy\.invoke</exclude>
                        <exclude>^org\.springframework\.cglib\.</exclude>
                        <exclude>^org\.springframework\.transaction\.</exclude>
                        <exclude>^org\.springframework\.validation\.</exclude>
                        <exclude>^org\.springframework\.app\.</exclude>
                        <exclude>^org\.springframework\.aop\.</exclude>
                        <exclude>^org\.springframework\.beans\.</exclude>
                        <exclude>^java\.lang\.reflect\.Method\.invoke</exclude>
                        <!-- Amazon SDK exclusions -->
                        <exclude>^software\.amazon\.awssdk\..*\.execute</exclude>
                        <exclude>^software\.amazon\.awssdk\..*\.invoke</exclude>
                        <exclude>^software\.amazon\.awssdk\..*\.call</exclude>
                        <!-- Other properties -->
                        <rootCauseFirst>true</rootCauseFirst>
                        <inlineHash>true</inlineHash>
                    </throwableConverter>
                </stackTrace>
                <arguments>
                    <includeNonStructuredArguments>true</includeNonStructuredArguments>
                    <includeStructuredArguments>true</includeStructuredArguments>
                </arguments>
            </providers>
        </encoder>
    </appender>

    <logger name="ca.bnc.payment.lib.service.HeapIndicator" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="ca.bnc.payment.lib.service.HeapCalculator" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <root level="${LOG_LEVEL}">
        <appender-ref ref="STDOUT"/>
    </root>

    <!-- Free resources after JVM shutdown -->
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

</configuration>
