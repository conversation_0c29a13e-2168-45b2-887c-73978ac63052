app:
  directParticipantIdentifier: ${DIRECT_PARTICIPANT_IDENTIFIER}
  name: ${APP_NAME}
applicationmanagement:
  thresholdheapinpercent: 95
health:
  config:
    enabled: true
logging:
  level:
    ca.bnc.payment: DEBUG
    ca.nbc.payment: DEBUG
    org.springframework: INFO
    org.springframework.web: INFO
management:
  endpoint:
    health:
      group:
        liveness:
          include: heapIndicator, ping
          show-details: always
        readiness:
          include: ping
          show-details: always
      show-details: always
  health:
    defaults:
      enabled: true
    mapping: null
    show-details: always
    web:
      exposure:
        include: '*'
  server:
    port: 9999
okta:
  jwks:
    authorizationServerId: ${JWKSAUTHORIZATIONSERVERID}
    client-id: ${OKTACLIENTID}
    private-key: ${OKTAPRIVATEKEY}
    url: ${JWKSURL}
  tokenDetails:
    limitsVelocityScope:
      scope: "system:limits:velocity:update"
    paymentRegistrationScope:
      scope: "pmt:registration-management:read"
providers:
  bank-account-connector:
    retry:
      attempts: ${BANK_ACCOUNT_CONNECTOR_RETRY_ATTEMPTS}
      backoff: ${BANK_ACCOUNT_CONNECTOR_RETRY_BACKOFF}
    url: ${BANK_ACCOUNT_CONNECTOR_URL}
  limits-velocity:
    retry:
      attempts: 3
      backoff: 100
    url: ${LIMITS_VELOCITY_URL}
  money-request:
    retry:
      attempts: 3
      backoff: 100
    url: ${MONEY_REQUEST_URL}
  payment-registration:
    retry:
      attempts: 4
      backoff: 100
    url: ${PAYMENT_REGISTRATION_URL}
server:
  port: 8080
  ssl:
    enabled: false
    key-alias: "UNUSED"
    key-password: "UNUSED"
    key-store-password: "UNUSED"
    key-store-type: "UNUSED"
    key-store: "UNUSED"
    trust-store-password: ${SERVER_SSL_TRUSTSTOREPASSWORD}
    trust-store: "/tmp/certs/truststore/nbc-root-truststore.jks"
spring:
  application:
    name: pmt-processing-simulation-api
    version: @project.version@
  cloud:
    openfeign:
      client:
        config:
          bankAccountConnectorApiClient:
            connectTimeout: 10000
            readTimeout: 10000
            logger-level: full
          limitsVelocityApiClient:
            connectTimeout: 10000
            readTimeout: 10000
            logger-level: full
          moneyRequestApiClient:
            connectTimeout: 10000
            readTimeout: 10000
            logger-level: full
          paymentRegistrationApiClient:
            connectTimeout: 10000
            readTimeout: 10000
            logger-level: full
      httpclient:
        disableSslValidation: ${DISABLE_SSL_VALIDATION}
      okhttp:
        enabled: true
  jackson:
    default-property-inclusion: non_null
    deserialization:
      ADJUST_DATES_TO_CONTEXT_TIME_ZONE: false
    serialization:
      WRITE_DATES_WITH_ZONE_ID: true
  profiles:
    include: ${SPRING_PROFILE_INCLUDE}
