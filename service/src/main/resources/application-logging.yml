logging:
  logDomain: PAYMENT_PROCESSING_SIMULATION
  config:
    api:
      [ "PUT::/et_pmt_proc/etransfer-payment-processing-simulation/{endToEndBusinessIdentification}" ]:
        provider:
          name: PAYMENT_PROCESSING_SIMULATION_API
          operation: SIMULATE_PAYMENT_PROCESSING
        request:
          allPathVariables: true
          headers:
            - x-client-id
            - x-request-id
          fields:
            - FIToFICustomerCreditTransfer>groupHeader_AS_groupHeader
            - FIToFICustomerCreditTransfer>creditTransferTransactionInformation>paymentIdentification>endToEndIdentification_AS_endToEndIdentification
            - FIToFICustomerCreditTransfer>creditTransferTransactionInformation>paymentIdentification>instructionIdentification_AS_instructionIdentification
        response:
          allHeaders: true
      ["PROVIDER:POST::/payments/{id}/debit"]:
        hostname: "${providers.bank-account-connector.url}"
        provider:
          name: <PERSON><PERSON><PERSON>_ACCOUNT_CONNECTOR
          operation: INITIATE_DEBIT
        request:
          allPathVariables: true
          headers:
            - x-channel-id
            - x-channel-type
            - x-action
            - x-payment
            - x-c1-client-id
            - x-retry-indicator
            - x-request-id
          body: true
        response:
          body: true
      ["PROVIDER:POST::/velocities/simulation"]:
        hostname: "${providers.limits-velocity.url}"
        provider:
          name: LIMIT_VELOCITY
          operation: SIMULATE_VELOCITY
        request:
          headers:
            - accept-version
            - x-channel-id
            - x-request-id
          fields:
            - simulationType
            - endToEndIdentification
            - instructionIdentification
            - clientId
            - limitType
            - amount
            - postingDate
          fieldsToResponse:
            - endToEndIdentification
            - instructionIdentification
            - clientId
      ["PROVIDER:GET::/et_pmt_proc/incoming-money-request/{interacMoneyRequestId}"]:
        hostname: "${providers.money-request.url}"
        provider:
          name: MONEY_REQUEST
          operation: GET_MONEY_REQUEST
        request:
          headers:
            - interacMoneyRequestId
            - x-channel-id
            - x-channel-type
            - x-client-id
            - x-request-id
            - traceparent
            - bncbusinesstraceid
          allPathVariables: true
          fieldsToResponse:
            - interacMoneyRequestId
            - bncbusinesstraceid
        response:
          allHeaders: true
          fields:
            - creditorPaymentActivationRequest>groupHeader>messageIdentification
            - creditorPaymentActivationRequest>paymentInformation>moneyRequestStatus
      ["PROVIDER:GET::/pmt_rail_net/registrations"]:
        hostname: "${providers.payment-registration.url}"
        provider:
          name: PAYMENT_REGISTRATION
          operation: GET_REGISTRATION
        request:
          allQueryVariables: true
          headers:
            - x-version
            - x-channel-type
            - x-request-id
            - x-channel-id
            - traceparent
            - tracestate
            - bncbusinesstraceid
        response:
          fields:
            - registrations>registrationId
            - registrations>clientType
            - registrations>railRegistration>railType
      ["PROVIDER:POST::/oauth2/{authorizationServerId}/v1/token"]:
        hostname: "${okta.jwks.url}"
        provider:
          name: OKTA_JWKS
          operation: GET_TOKEN
        request:
          body: true
          allHeaders: true
          allPathVariables: true
          allQueryVariables: true
        response:
          body: true
          allHeaders: true
          allPathVariables: true
          allQueryVariables: true