app:
  directParticipantIdentifier: CA000006
  name: pmt-processing-simulation-api
applicationmanagement:
  thresholdheapinpercent: 95
logging:
  level:
    ca.bnc.payment: INFO
    ca.nbc.payment: INFO
    org.springframework: INFO
    org.springframework.web: INFO
management:
  endpoint:
    health:
      group:
        liveness:
          include: heapIndicator, ping
          show-details: always
        readiness:
          include: ping
          show-details: always
      show-details: always
  health:
    defaults:
      enabled: true
    jms:
      enabled: true
    mapping: null
    show-details: always
    web:
      exposure:
        include: '*'
  server:
    port: 9999
okta:
  enabled: false
  jwks:
    authorizationServerId: "fake_auth_server_id"
    clientId: "clientID"
    privateKey: "privateKey"
    url: "fake_jwks_url"
providers:
  bank-account-connector:
    retry:
      attempts: 3
      backoff: 100
    url: http://localhost:8114
  limits-velocity:
    retry:
      attempts: 4
      backoff: 100
    url: http://localhost:8111
  money-request:
    retry:
      attempts: 4
      backoff: 100
    url: http://localhost:8112
  payment-registration:
    retry:
      attempts: 4
      backoff: 100
    url: http://localhost:8113