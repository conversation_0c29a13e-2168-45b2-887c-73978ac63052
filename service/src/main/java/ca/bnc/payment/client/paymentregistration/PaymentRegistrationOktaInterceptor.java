package ca.bnc.payment.client.paymentregistration;

import ca.bnc.payment.client.interceptor.ConfigTokenProvider;
import ca.bnc.payment.client.interceptor.SimpleOktaInterceptor;
import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import feign.RequestInterceptor;
import feign.RequestTemplate;

public final class PaymentRegistrationOktaInterceptor implements RequestInterceptor {

    private static final String PAYMENT_REGISTRATION_SCOPE = "paymentRegistrationScope";
    private final RequestInterceptor delegate;

    public PaymentRegistrationOktaInterceptor(final OktaClientTokenManager oktaClientTokenManager) {
       this.delegate = new SimpleOktaInterceptor(new ConfigTokenProvider(oktaClientTokenManager, PAYMENT_REGISTRATION_SCOPE));
    }

    @Override
    public void apply(final RequestTemplate template) {
        delegate.apply(template);
    }
}
