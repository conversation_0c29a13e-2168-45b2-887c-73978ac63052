package ca.bnc.payment.client.moneyrequest;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.rest.EtPmtProcApi;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "moneyRequestApiClient",
        url = "${providers.money-request.url}",
        configuration = {MoneyRequestDecoder.class})
public interface MoneyRequestApiClient extends EtPmtProcApi {

}
