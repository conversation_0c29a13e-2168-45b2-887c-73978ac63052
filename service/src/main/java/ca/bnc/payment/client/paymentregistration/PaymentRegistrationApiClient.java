package ca.bnc.payment.client.paymentregistration;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_payment_registration_resources.generated.rest.PmtRailNetApi;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "paymentRegistrationApiClient",
        url = "${providers.payment-registration.url}",
        configuration = {PaymentRegistrationDecoder.class, PaymentRegistrationOktaInterceptor.class})
public interface PaymentRegistrationApiClient extends PmtRailNetApi {

}
