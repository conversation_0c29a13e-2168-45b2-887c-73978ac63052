package ca.bnc.payment.client.bankaccountconnector;
import ca.nbc.payment.etransfer.bankaccountconnector.api.PaymentsApi;
import org.springframework.cloud.openfeign.FeignClient;


@FeignClient(name = "bankAccountConnectorApiClient",
        url = "${providers.bank-account-connector.url}",
        configuration = {BankAccountConnectorDecoder.class})
public interface BankAccountConnectorApiClient extends PaymentsApi {
}
