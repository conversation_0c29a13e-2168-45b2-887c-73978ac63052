package ca.bnc.payment.client.paymentregistration;

import ca.bnc.payment.client.common.ErrorBuilder;
import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.exception.RetryablePaymentSimulationException;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

import static ca.bnc.payment.utils.ErrorConstants.CUSTOMER_NOT_EXIST_CODE;
import static ca.bnc.payment.utils.ErrorConstants.CUSTOMER_NOT_EXIST_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.PAYMENT_REGISTRATION_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.REGISTRATION_API_UNAVAILABLE_LOG;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;

@RequiredArgsConstructor
public class PaymentRegistrationDecoder implements ErrorDecoder {

    private static final String API_NAME = "Payment Registration API";

    private final ErrorBuilder errorBuilder;

    @Override
    public Exception decode(final String methodKey, final Response response) {
        final HttpStatus httpStatus = HttpStatus.valueOf(response.status());
        Error error = errorBuilder.buildErrorFromResponseBody(response, API_NAME);

        return switch (httpStatus) {
            case BAD_REQUEST -> PaymentSimulationException.badRequest(error);
            case NOT_FOUND -> PaymentSimulationException.notFound(
                    CUSTOMER_NOT_EXIST_CODE,
                    CUSTOMER_NOT_EXIST_LOG,
                    PAYMENT_REGISTRATION_SERVICE_ORIGIN,
                    NA
            );
            case SERVICE_UNAVAILABLE -> new RetryablePaymentSimulationException(
                    TECHNICAL_ERROR_CODE,
                    REGISTRATION_API_UNAVAILABLE_LOG,
                    PAYMENT_REGISTRATION_SERVICE_ORIGIN,
                    NA
            );
            default -> PaymentSimulationException.internalServerError(error);
        };
    }
}
