package ca.bnc.payment.client.limitvelocity;

import ca.bnc.payment.client.interceptor.ConfigTokenProvider;
import ca.bnc.payment.client.interceptor.SimpleOktaInterceptor;
import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import feign.RequestInterceptor;
import feign.RequestTemplate;

public final class LimitsVelocityOktaInterceptor implements RequestInterceptor {

    private static final String LIMITS_VELOCITY_TOKEN_SCOPE = "limitsVelocityScope";
    private final RequestInterceptor delegate;

    public LimitsVelocityOktaInterceptor(final OktaClientTokenManager oktaClientTokenManager) {

        this.delegate = new SimpleOktaInterceptor(
                new ConfigTokenProvider(oktaClientTokenManager, LIMITS_VELOCITY_TOKEN_SCOPE));
    }

    @Override
    public void apply(final RequestTemplate template) {
        delegate.apply(template);
    }
}
