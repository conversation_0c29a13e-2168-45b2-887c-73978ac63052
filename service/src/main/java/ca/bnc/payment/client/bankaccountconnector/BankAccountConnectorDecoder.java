package ca.bnc.payment.client.bankaccountconnector;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.exception.RetryablePaymentSimulationException;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_BAD_REQUEST;
import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_INTERNAL_SERVER_ERROR;
import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_SERVICE_UNAVAILABLE;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;

@RequiredArgsConstructor
public class BankAccountConnectorDecoder implements ErrorDecoder {
    private final BankAccountConnectorDecoderErrorsBuilder errorBuilder;

    @Override
    public Exception decode(final String methodKey, final Response response) {
        final HttpStatus httpStatus = HttpStatus.valueOf(response.status());
        Error error = errorBuilder.buildErrorFromResponseBody(response);

        return switch (httpStatus) {
            case BAD_REQUEST -> PaymentSimulationException.badRequest(
                    error.text("%s : %s".formatted(BANK_ACCOUNT_CONNECTOR_BAD_REQUEST, error.getText()))
            );
            case INTERNAL_SERVER_ERROR -> PaymentSimulationException.internalServerError(
                    TECHNICAL_ERROR_CODE,
                    BANK_ACCOUNT_CONNECTOR_INTERNAL_SERVER_ERROR,
                    BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN,
                    NA
            );
            case SERVICE_UNAVAILABLE -> new RetryablePaymentSimulationException(
                    TECHNICAL_ERROR_CODE,
                    BANK_ACCOUNT_CONNECTOR_SERVICE_UNAVAILABLE,
                    BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN,
                    NA
            );
            default -> PaymentSimulationException.internalServerError(error);
        };
    }
}
