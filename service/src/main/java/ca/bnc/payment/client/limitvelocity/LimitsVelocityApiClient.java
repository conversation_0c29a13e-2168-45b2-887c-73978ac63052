package ca.bnc.payment.client.limitvelocity;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.rest.VelocitiesApi;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "limitsVelocityApiClient",
        url = "${providers.limits-velocity.url}",
        configuration = {LimitsVelocityDecoder.class, LimitsVelocityOktaInterceptor.class})
public interface LimitsVelocityApiClient extends VelocitiesApi {
}
