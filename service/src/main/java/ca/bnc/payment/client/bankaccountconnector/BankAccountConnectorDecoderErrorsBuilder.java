package ca.bnc.payment.client.bankaccountconnector;

import ca.bnc.payment.normalization.lib.service.NormalizationService;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ErrorModel;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.Optional;

import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_ERR_TABLE;
import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_INTERNAL_SERVER_ERROR;
import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;

@Component
@AllArgsConstructor
public class BankAccountConnectorDecoderErrorsBuilder {

    private static final Logger LOGGER = LoggerFactory.getLogger(BankAccountConnectorDecoderErrorsBuilder.class);

    private static final Error DEFAULT_ERROR = new Error()
            .code(TECHNICAL_ERROR_CODE)
            .text(BANK_ACCOUNT_CONNECTOR_INTERNAL_SERVER_ERROR)
            .origin(BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN)
            .rule(NA);

    private final ObjectMapper objectMapper;
    private final LoggingFacade loggingFacade;
    private final NormalizationService normalizationService;

    public Error buildErrorFromResponseBody(final Response response) {
        return Optional.ofNullable(response.body())
                .map(this::getError)
                .orElse(DEFAULT_ERROR);
    }

    private Error getError(final Response.Body body) {
        Error error = null;
        try (InputStream inputStream = body.asInputStream()) {
            final ErrorModel bacError = objectMapper.readValue(inputStream, ErrorModel.class);
            error = new Error()
                    .code(
                            normalizationService.normalize(
                                    BANK_ACCOUNT_CONNECTOR_ERR_TABLE,
                                    bacError.getCode()).orElse(TECHNICAL_ERROR_CODE)
                    )
                    .text(bacError.getText())
                    .origin(BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN)
                    .rule(NA);
        } catch (final Exception exception) {
            loggingFacade.error(LOGGER, "An error occurred while parsing error response from bank account connector API",
                    exception);
        }
        return error;
    }
}
