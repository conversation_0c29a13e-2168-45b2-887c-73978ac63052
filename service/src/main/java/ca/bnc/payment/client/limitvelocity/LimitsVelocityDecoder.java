package ca.bnc.payment.client.limitvelocity;

import ca.bnc.payment.client.common.ErrorBuilder;
import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

@RequiredArgsConstructor
public class LimitsVelocityDecoder implements ErrorDecoder {

    private static final String API_NAME = "Limits Velocity API";

    private final ErrorBuilder errorBuilder;

    @Override
    public Exception decode(final String methodKey, final Response response) {
        HttpStatus httpStatus = HttpStatus.valueOf(response.status());
        Error error = errorBuilder.buildErrorFromResponseBody(response, API_NAME);

        if (httpStatus == HttpStatus.BAD_REQUEST) {
            return PaymentSimulationException.badRequest(error);
        }
        return PaymentSimulationException.internalServerError(error);
    }

}
