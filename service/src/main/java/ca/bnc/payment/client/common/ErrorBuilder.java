package ca.bnc.payment.client.common;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Errors;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class ErrorBuilder {

    private static final Logger LOGGER = LoggerFactory.getLogger(ErrorBuilder.class);
    private final ObjectMapper objectMapper;
    private final LoggingFacade loggingFacade;

    public Error buildErrorFromResponseBody(final Response response, final String apiName) {
        return Optional.ofNullable(response.body()).map(body -> getError(body, apiName)).orElse(null);
    }

    private Error getError(final Response.Body body, final String apiName) {
        try (InputStream inputStream = body.asInputStream()) {
            final Errors errors = objectMapper.readValue(inputStream, Errors.class);

            return errors.getErrors().stream()
                    .findFirst()
                    .map(error -> new Error()
                            .code(error.getCode())
                            .text(error.getText())
                            .origin(error.getOrigin())
                            .rule(error.getRule()))
                    .orElse(null);
        } catch (final Exception exception) {
            loggingFacade.error(LOGGER, "An error occurred while parsing error response from the " + apiName, exception);
            return null;
        }
    }
}
