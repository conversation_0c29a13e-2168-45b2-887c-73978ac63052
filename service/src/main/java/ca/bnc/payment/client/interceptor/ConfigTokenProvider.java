package ca.bnc.payment.client.interceptor;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import ca.nbc.payment.pmt_security_library.utils.OktaUtil;

import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;
import static ca.bnc.payment.utils.ErrorConstants.TOKEN_GENERATION_FAILED_LOG;
import static java.util.Objects.isNull;

public record ConfigTokenProvider(OktaClientTokenManager oktaClientTokenManager,
                                  String tokenConfigName) implements TokenProvider {
    @Override
    public String getToken() {
        String oktaToken = OktaUtil.getOktaToken(oktaClientTokenManager, tokenConfigName);
        if (isNull(oktaToken)) {
            throw PaymentSimulationException.internalServerError(
                    TECHNICAL_ERROR_CODE,
                    TOKEN_GENERATION_FAILED_LOG,
                    SERVICE_ORIGIN,
                    NA
            );
        }
        return oktaToken;
    }
}
