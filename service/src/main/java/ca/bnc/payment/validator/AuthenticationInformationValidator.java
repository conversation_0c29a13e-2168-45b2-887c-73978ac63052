package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.PaymentAuthentication;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.bnc.payment.utils.ChannelIdUtil;
import ca.bnc.payment.utils.ParticipantIdUtil;
import ca.bnc.payment.utils.PaymentRequestUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Set;

import static ca.bnc.payment.utils.ErrorConstants.PAYMENT_AUTH_REQUEST_INVALID_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.PAYMENT_AUTH_REQUEST_INVALID_CODE;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static ca.bnc.payment.utils.PaymentValidationOrderValues.TWO;
import static java.util.Objects.isNull;

@Component
@RequiredArgsConstructor
@Order(TWO)
public class AuthenticationInformationValidator implements PaymentRequestValidator {

    private final ParticipantIdUtil participantIdUtil;
    private final ChannelIdUtil channelIdUtil;
    private final PaymentRequestUtil paymentRequestUtil;

    private static final Set<LocalInstrument2Choice.ProprietaryEnum> ACCOUNT_ALIAS_PAYMENT_TYPES = Set.of(
            LocalInstrument2Choice.ProprietaryEnum.REGULAR_PAYMENT,
            LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT,
            LocalInstrument2Choice.ProprietaryEnum.REALTIME_ACCOUNT_ALIAS_PAYMENT);

    @Override
    public void validate(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        if (isAuthenticationMissingForAliasPayment(paymentSimulationRequestContext)) {
            throw PaymentSimulationException.badRequest(
                    PAYMENT_AUTH_REQUEST_INVALID_CODE,
                    PAYMENT_AUTH_REQUEST_INVALID_LOG,
                    SERVICE_ORIGIN, NA);
        }
    }

    @Override
    public boolean isApplicable(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return channelIdUtil.isBneChannelId(paymentSimulationRequestContext.paymentSimulationHeader().xChannelId())
                && participantIdUtil.isBncParticipant();
    }

    private boolean isAuthenticationMissingForAliasPayment(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return ACCOUNT_ALIAS_PAYMENT_TYPES.contains(paymentRequestUtil.getProprietary(paymentSimulationRequestContext.paymentRequest()))
                && isNull(getPaymentAuthentication(paymentSimulationRequestContext.paymentRequest()));
    }

    private static PaymentAuthentication getPaymentAuthentication(final SendPaymentExecuteRequest sendPaymentExecuteRequest) {
        return sendPaymentExecuteRequest
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getSupplementaryData()
                .getPaymentAuthentication();
    }
}
