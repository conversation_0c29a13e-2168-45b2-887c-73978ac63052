package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.RemittanceInformation16;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static ca.bnc.payment.utils.ErrorConstants.INVALID_REMITTANCE_REGEX_CODE;
import static ca.bnc.payment.utils.ErrorConstants.INVALID_REMITTANCE_REGEX_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static ca.bnc.payment.utils.PaymentValidationOrderValues.EIGHT;
import static org.springframework.util.CollectionUtils.isEmpty;

@Component
@Order(EIGHT)
public class RemittanceRegExValidator implements PaymentRequestValidator {

    private static final int MAX_LENGTH_REMITTANCE_DATA_LINE = 140;
    private static final String CARRIAGE_RETURN_REGEX = "[\\r\\n]";
    private static final String BLANK_SPACE = " ";
    private static final String SANITIZE_REGEX =
            "^((?!(#|\\&|\\\\|%|\\<|\\>|http\\:|https\\:|www|function|return|javascript|select|drop|truncate)).)*$";

    @Override
    public void validate(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        List<String> remittanceUnstructured = getRemittanceUnstructured(paymentSimulationRequestContext.paymentRequest());
        if (!isEmpty(remittanceUnstructured)) {
            validateRemittanceLines(remittanceUnstructured);
            validateRemittanceContent(remittanceUnstructured);
        }
    }

    private void validateRemittanceContent(final Iterable<String> remittanceUnstructured) {
        boolean isRegExMatched = String.join("", remittanceUnstructured)
                .toLowerCase()
                .replaceAll(CARRIAGE_RETURN_REGEX, BLANK_SPACE)
                .matches(SANITIZE_REGEX);

        if (!isRegExMatched) {
            throw PaymentSimulationException.badRequest(
                    INVALID_REMITTANCE_REGEX_CODE,
                    INVALID_REMITTANCE_REGEX_LOG,
                    SERVICE_ORIGIN,
                    NA);
        }
    }

    private void validateRemittanceLines(final Collection<String> remittanceLines) {
        boolean hasInvalidLine = remittanceLines.stream()
                .anyMatch(line -> line.isEmpty() || line.length() > MAX_LENGTH_REMITTANCE_DATA_LINE);

        if (hasInvalidLine) {
            throw createRemittanceValidationException();
        }
    }

    private PaymentSimulationException createRemittanceValidationException() {
        return PaymentSimulationException.badRequest(
                INVALID_REMITTANCE_REGEX_CODE,
                INVALID_REMITTANCE_REGEX_LOG,
                SERVICE_ORIGIN,
                NA);
    }


    private List<String> getRemittanceUnstructured(final SendPaymentExecuteRequest sendPaymentExecuteRequest) {
        return Optional.ofNullable(sendPaymentExecuteRequest.getFiToFICustomerCreditTransfer().getCreditTransferTransactionInformation())
                .map(CreditTransferTransaction39::getRemittanceInformation)
                .map(RemittanceInformation16::getUnstructured)
                .orElse(Collections.emptyList());
    }
}


