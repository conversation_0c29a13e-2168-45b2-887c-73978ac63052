package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.MandateRelatedInformation;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.bnc.payment.utils.ChannelIdUtil;
import ca.bnc.payment.utils.ParticipantIdUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static ca.bnc.payment.utils.ErrorConstants.MANDATE_RELATED_INFORMATION_MISSING_CODE;
import static ca.bnc.payment.utils.ErrorConstants.MANDATE_RELATED_INFORMATION_MISSING_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static ca.bnc.payment.utils.PaymentValidationOrderValues.THREE;
import static java.util.Objects.isNull;

@Component
@RequiredArgsConstructor
@Order(THREE)
public class DeferredPaymentValidator implements PaymentRequestValidator {
    private final ParticipantIdUtil participantIdUtil;
    private final ChannelIdUtil channelIdUtil;

    @Override
    public void validate(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        if (isNull(getMandateRelatedInformation(paymentSimulationRequestContext.paymentRequest()))) {
            throw PaymentSimulationException.badRequest(
                    MANDATE_RELATED_INFORMATION_MISSING_CODE,
                    MANDATE_RELATED_INFORMATION_MISSING_LOG,
                    SERVICE_ORIGIN,
                    NA);
        }
    }

    @Override
    public boolean isApplicable(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return channelIdUtil.isDeferredChannelId(paymentSimulationRequestContext.paymentSimulationHeader().xChannelId())
                && participantIdUtil.isBncParticipant();
    }


    private MandateRelatedInformation getMandateRelatedInformation(final SendPaymentExecuteRequest sendPaymentExecuteRequest) {
        return sendPaymentExecuteRequest
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getMandateRelatedInformation();
    }
}
