package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SupplementaryData.ClientTypeEnum;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.UUID;

import static ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SupplementaryData.ClientTypeEnum.ORGANISATION;
import static ca.bnc.payment.utils.ErrorConstants.CREDITOR_ID_MISSING_CODE;
import static ca.bnc.payment.utils.ErrorConstants.CREDITOR_ID_MISSING_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static ca.bnc.payment.utils.PaymentValidationOrderValues.NINE;

@Component
@Order(NINE)
public class CreditorIdValidator implements PaymentRequestValidator {

    @Override
    public void validate(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        if (isClientTypeOrganisationWithoutCreditorId(paymentSimulationRequestContext)) {
            throw PaymentSimulationException.badRequest(CREDITOR_ID_MISSING_CODE,
                    CREDITOR_ID_MISSING_LOG,
                    SERVICE_ORIGIN,
                    NA);
        }
    }

    private boolean isClientTypeOrganisationWithoutCreditorId(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return isClientTypeOrganisation(paymentSimulationRequestContext)
                && getCreditorId(paymentSimulationRequestContext.paymentRequest()) == null;
    }

    private boolean isClientTypeOrganisation(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return getClientType(paymentSimulationRequestContext.paymentRequest()) == ORGANISATION;
    }

    private UUID getCreditorId(final SendPaymentExecuteRequest paymentRequest) {
        return paymentRequest.getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getSupplementaryData()
                .getCreditorId();
    }

    private ClientTypeEnum getClientType(final SendPaymentExecuteRequest paymentRequest) {
        return paymentRequest.getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getSupplementaryData()
                .getClientType();
    }
}
