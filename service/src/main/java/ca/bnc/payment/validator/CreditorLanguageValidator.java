package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SupplementaryData;
import ca.bnc.payment.utils.PaymentRequestUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static ca.bnc.payment.utils.ErrorConstants.INVALID_CREDITOR_LANGUAGE_CODE;
import static ca.bnc.payment.utils.ErrorConstants.INVALID_CREDITOR_LANGUAGE_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static ca.bnc.payment.utils.PaymentValidationOrderValues.FIVE;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Component
@RequiredArgsConstructor
@Order(FIVE)
public class CreditorLanguageValidator implements PaymentRequestValidator {

    private final PaymentRequestUtil paymentRequestUtil;

    @Override
    public void validate(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        if (isLanguageMissingWithContactDetails(paymentSimulationRequestContext)) {
            throw PaymentSimulationException.badRequest(INVALID_CREDITOR_LANGUAGE_CODE,
                    INVALID_CREDITOR_LANGUAGE_LOG,
                    SERVICE_ORIGIN,
                    NA);
        }

    }

    private boolean isLanguageMissingWithContactDetails(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return hasMobileOrEmail(paymentSimulationRequestContext)
                && missingLanguage(paymentSimulationRequestContext);
    }

    private boolean missingLanguage(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return isNull(getCreditorPreferredLanguage(paymentSimulationRequestContext.paymentRequest()));
    }

    private boolean hasMobileOrEmail(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return nonNull(paymentRequestUtil.getMobileNumber(paymentSimulationRequestContext.paymentRequest()))
                || nonNull(paymentRequestUtil.getEmailAddress(paymentSimulationRequestContext.paymentRequest()));
    }

    private SupplementaryData.CreditorPreferredLanguageEnum getCreditorPreferredLanguage(final SendPaymentExecuteRequest paymentRequest) {
        return paymentRequest.getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getSupplementaryData()
                .getCreditorPreferredLanguage();
    }


}
