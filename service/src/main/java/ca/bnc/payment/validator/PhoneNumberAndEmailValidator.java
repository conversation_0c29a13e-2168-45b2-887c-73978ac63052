package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.utils.PaymentRequestUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Set;

import static ca.bnc.payment.utils.ErrorConstants.CONTACT_DETAILS_MISSING_CODE;
import static ca.bnc.payment.utils.ErrorConstants.CONTACT_DETAILS_MISSING_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static ca.bnc.payment.utils.PaymentValidationOrderValues.SEVEN;

@Component
@RequiredArgsConstructor
@Order(SEVEN)
public class PhoneNumberAndEmailValidator implements PaymentRequestValidator {
    private final PaymentRequestUtil paymentRequestUtil;
    private static final Set<LocalInstrument2Choice.ProprietaryEnum> ACCOUNT_ALIAS_PAYMENT_TYPES = Set.of(
            LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT,
            LocalInstrument2Choice.ProprietaryEnum.REALTIME_ACCOUNT_ALIAS_PAYMENT,
            LocalInstrument2Choice.ProprietaryEnum.REGULAR_PAYMENT);

    @Override
    public void validate(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        if (isContactDetailsMissingForAliasPayment(paymentSimulationRequestContext)) {
            throw PaymentSimulationException.badRequest(
                    CONTACT_DETAILS_MISSING_CODE,
                    CONTACT_DETAILS_MISSING_LOG,
                    SERVICE_ORIGIN,
                    NA);
        }

    }

    private boolean isContactDetailsMissingForAliasPayment(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return ACCOUNT_ALIAS_PAYMENT_TYPES.contains(paymentRequestUtil.getProprietary(paymentSimulationRequestContext.paymentRequest()))
                && paymentRequestUtil.getMobileNumber(paymentSimulationRequestContext.paymentRequest()) == null
                && paymentRequestUtil.getEmailAddress(paymentSimulationRequestContext.paymentRequest()) == null;
    }
}
