package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditorIdentification;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.bnc.payment.utils.PaymentRequestUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.Set;

import static ca.bnc.payment.utils.ErrorConstants.CREDITOR_NAME_IS_MISSING_LOG;
import static ca.bnc.payment.utils.ErrorConstants.CREDITOR_NAME_MISSING_CODE;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static ca.bnc.payment.utils.PaymentValidationOrderValues.FOUR;
import static java.util.Objects.isNull;

@Component
@RequiredArgsConstructor
@Order(FOUR)
public class CreditorNameValidator implements PaymentRequestValidator {
    private final PaymentRequestUtil paymentRequestUtil;
    private static final Set<LocalInstrument2Choice.ProprietaryEnum> ACCOUNT_ALIAS_PAYMENT_TYPES = Set.of(
            LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT,
            LocalInstrument2Choice.ProprietaryEnum.REALTIME_ACCOUNT_ALIAS_PAYMENT,
            LocalInstrument2Choice.ProprietaryEnum.REGULAR_PAYMENT);

    @Override
    public void validate(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        if (isCreditorNameMissingForAliasPayment(paymentSimulationRequestContext)) {
            throw PaymentSimulationException.badRequest(CREDITOR_NAME_MISSING_CODE,
                    CREDITOR_NAME_IS_MISSING_LOG,
                    SERVICE_ORIGIN,
                    NA);
        }
    }

    private boolean isCreditorNameMissingForAliasPayment(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return ACCOUNT_ALIAS_PAYMENT_TYPES.contains(paymentRequestUtil.getProprietary(paymentSimulationRequestContext.paymentRequest()))
                && isNull(getCreditorName(paymentSimulationRequestContext.paymentRequest()));
    }

    private static String getCreditorName(final SendPaymentExecuteRequest paymentRequest) {
        return Optional.ofNullable(paymentRequest.getFiToFICustomerCreditTransfer().getCreditTransferTransactionInformation())
                .map(CreditTransferTransaction39::getCreditor)
                .map(CreditorIdentification::getName)
                .orElse(null);
    }
}
