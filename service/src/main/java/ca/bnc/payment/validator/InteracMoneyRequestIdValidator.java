package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.utils.ChannelIdUtil;
import ca.bnc.payment.utils.ParticipantIdUtil;
import ca.bnc.payment.utils.PaymentRequestUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice.ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT;
import static ca.bnc.payment.utils.ErrorConstants.INTERAC_MONEY_REQUEST_ID_MISSING_CODE;
import static ca.bnc.payment.utils.ErrorConstants.INTERAC_MONEY_REQUEST_ID_MISSING_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static ca.bnc.payment.utils.PaymentValidationOrderValues.TEN;

@Component
@RequiredArgsConstructor
@Order(TEN)
public class InteracMoneyRequestIdValidator implements PaymentRequestValidator {

    private final ParticipantIdUtil participantIdUtil;
    private final ChannelIdUtil channelIdUtil;
    private final PaymentRequestUtil paymentRequestUtil;

    @Override
    public boolean isApplicable(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return channelIdUtil.isBneChannelId(paymentSimulationRequestContext.paymentSimulationHeader().xChannelId())
                && participantIdUtil.isBncParticipant()
                && FULFILL_REQUEST_FOR_PAYMENT == paymentRequestUtil.getProprietary(paymentSimulationRequestContext.paymentRequest());
    }

    @Override
    public void validate(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        final String interacMoneyRequestId = paymentSimulationRequestContext
                .paymentRequest()
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getSupplementaryData()
                .getInteracMoneyRequestId();
        if (interacMoneyRequestId == null) {
            throw PaymentSimulationException.badRequest(
                    INTERAC_MONEY_REQUEST_ID_MISSING_CODE,
                    INTERAC_MONEY_REQUEST_ID_MISSING_LOG,
                    SERVICE_ORIGIN,
                    NA
            );
        }
    }

}
