package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.utils.PaymentRequestUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static ca.bnc.payment.utils.ErrorConstants.CONTACT_DETAILS_INVALID_CODE;
import static ca.bnc.payment.utils.ErrorConstants.CONTACT_DETAILS_INVALID_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static ca.bnc.payment.utils.PaymentValidationOrderValues.SIX;

@Component
@RequiredArgsConstructor
@Order(SIX)
public class PhoneNumberAndEmailAddressBothPresentValidator implements PaymentRequestValidator {

    private final PaymentRequestUtil paymentRequestUtil;

    @Override
    public void validate(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        if (isEmailAddressPresent(paymentSimulationRequestContext)
                && isMobileNumberPresent(paymentSimulationRequestContext)) {
            throw PaymentSimulationException.badRequest(CONTACT_DETAILS_INVALID_CODE,
                    CONTACT_DETAILS_INVALID_LOG,
                    SERVICE_ORIGIN,
                    NA);

        }

    }

    private boolean isMobileNumberPresent(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return paymentRequestUtil.getMobileNumber(paymentSimulationRequestContext.paymentRequest()) != null;
    }

    private boolean isEmailAddressPresent(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return paymentRequestUtil.getEmailAddress(paymentSimulationRequestContext.paymentRequest()) != null;
    }
}
