package ca.bnc.payment.config;

import ca.bnc.payment.normalization.lib.service.NormalizationService;
import ca.bnc.payment.normalization.lib.service.impl.NormalizationServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Clock;

@Configuration
public class AppConfig {

    @Bean
    public Clock clock() {
        return Clock.systemUTC();
    }

    @Bean
    public NormalizationService normalizationService() {
        return new NormalizationServiceImpl();
    }

}
