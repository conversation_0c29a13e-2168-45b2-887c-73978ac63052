package ca.bnc.payment.service;

import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PaymentSimulationOrchestrator {

    private final PaymentRequestValidationService paymentRequestValidationService;
    private final InteracRegistrationValidationService interacRegistrationValidationService;
    private final LimitsValidationService limitsValidationService;
    private final MoneyRequestValidationService moneyRequestValidationService;
    private final BankAccountConnectorService bankAccountConnectorService;

    public void simulatePayment(final SendPaymentExecuteRequest paymentRequest,
                                final String endToEndBusinessIdentification,
                                final PaymentSimulationHeader paymentSimulationHeader) {

        final PaymentSimulationRequestContext paymentSimulationRequestContext = new PaymentSimulationRequestContext(paymentRequest,
                paymentSimulationHeader,
                endToEndBusinessIdentification);

        paymentRequestValidationService.validatePaymentRequest(paymentSimulationRequestContext);
        interacRegistrationValidationService.validateInteracRegistration(paymentSimulationRequestContext);
        limitsValidationService.validateLimits(paymentSimulationRequestContext);
        moneyRequestValidationService.validateIncomingMoneyRequest(paymentSimulationRequestContext);
        bankAccountConnectorService.initiateDebit(paymentSimulationRequestContext, endToEndBusinessIdentification);
    }

}
