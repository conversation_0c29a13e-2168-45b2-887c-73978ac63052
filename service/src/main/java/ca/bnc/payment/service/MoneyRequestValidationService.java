package ca.bnc.payment.service;

import ca.bnc.payment.adapter.moneyrequest.MoneyRequestAdapter;
import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.model.DomesticFulfillmentMoneyRequest;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.model.MoneyRequestStatus;
import ca.bnc.payment.utils.ChannelIdUtil;
import ca.bnc.payment.utils.ParticipantIdUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import static ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.model.MoneyRequestStatus.AVAILABLE_TO_BE_FULFILLED;
import static ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice.ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT;
import static ca.bnc.payment.utils.ErrorConstants.CANNOT_DUE_REQUEST_STATUS_CODE;
import static ca.bnc.payment.utils.ErrorConstants.CANNOT_DUE_REQUEST_STATUS_LOG;
import static ca.bnc.payment.utils.ErrorConstants.MONEY_REQUEST_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.NA;

@Service
@RequiredArgsConstructor
public class MoneyRequestValidationService {

    private final MoneyRequestAdapter moneyRequestAdapter;
    private final ChannelIdUtil channelIdUtil;
    private final ParticipantIdUtil participantIdUtil;

    private static final MoneyRequestStatus VALID_MONEY_REQUEST_STATUS = AVAILABLE_TO_BE_FULFILLED;

    public void validateIncomingMoneyRequest(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        if (isApplicable(paymentSimulationRequestContext)) {
            final DomesticFulfillmentMoneyRequest moneyRequestResponse =
                    moneyRequestAdapter.getIncomingMoneyRequest(paymentSimulationRequestContext).getBody();
            validateMoneyRequestStatusIsValid(moneyRequestResponse);
        }
    }

    private boolean isApplicable(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return channelIdUtil.isBneChannelId(paymentSimulationRequestContext.paymentSimulationHeader().xChannelId())
                && participantIdUtil.isBncParticipant()
                && FULFILL_REQUEST_FOR_PAYMENT == paymentSimulationRequestContext
                .paymentRequest()
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getPaymentTypeInformation()
                .getLocalInstrument()
                .getProprietary();
    }

    private void validateMoneyRequestStatusIsValid(final DomesticFulfillmentMoneyRequest moneyRequestResponse) {
        if (moneyRequestResponse != null
                && moneyRequestResponse.getCreditorPaymentActivationRequest() != null
                && VALID_MONEY_REQUEST_STATUS != moneyRequestResponse
                .getCreditorPaymentActivationRequest()
                .getPaymentInformation()
                .getMoneyRequestStatus()
        ) {
            throw PaymentSimulationException.badRequest(
                    CANNOT_DUE_REQUEST_STATUS_CODE,
                    CANNOT_DUE_REQUEST_STATUS_LOG,
                    MONEY_REQUEST_SERVICE_ORIGIN,
                    NA
            );
        }
    }

}
