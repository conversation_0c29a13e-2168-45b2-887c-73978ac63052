package ca.bnc.payment.service;

import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.validator.PaymentRequestValidator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class PaymentRequestValidationService {

    private final List<PaymentRequestValidator> validators;

    public void validatePaymentRequest(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        validators.stream().filter(validator -> validator.isApplicable(paymentSimulationRequestContext))
                .forEach(validator -> validator.validate(paymentSimulationRequestContext));
    }

}
