package ca.bnc.payment.service;

import ca.bnc.payment.adapter.bankaccountconnector.BankAccountConnectorAdapter;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.MandateRelatedSupplementaryData;
import ca.bnc.payment.utils.PaymentRequestUtil;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ChannelType;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BankAccountConnectorService {

    private final PaymentRequestUtil paymentRequestUtil;
    private final BankAccountConnectorAdapter bankAccountConnectorAdapter;

    public void initiateDebit(
            final PaymentSimulationRequestContext paymentSimulationRequestContext,
            final String endToEndBusinessIdentification) {
        bankAccountConnectorAdapter.initiateDebit(
                endToEndBusinessIdentification,
                paymentSimulationRequestContext,
                getChannelType(paymentSimulationRequestContext),
                getPaymentType(paymentSimulationRequestContext)
        );
    }

    private ChannelType getChannelType(final PaymentSimulationRequestContext context) {
        final ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType headerChannelType =
                context.paymentSimulationHeader().xChannelType();

        if (headerChannelType
                == ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType.BATCH) {
            final MandateRelatedSupplementaryData.OriginalChannelTypeEnum payloadChannelType =
                    paymentRequestUtil.getOriginalChannelType(context.paymentRequest());

            return (payloadChannelType != null)
                    ? mapToBankAccountChannelType(payloadChannelType)
                    : mapToBankAccountChannelType(headerChannelType);
        } else {
            return mapToBankAccountChannelType(headerChannelType);
        }
    }

    private PaymentType getPaymentType(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        return mapToBankAccountPaymentType(paymentRequestUtil.getProprietary(paymentSimulationRequestContext.paymentRequest()));
    }

    private ChannelType mapToBankAccountChannelType(
            final ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType
                    headerChannelType) {
        return switch (headerChannelType) {
            case BATCH -> ChannelType.BATCH;
            case WEB -> ChannelType.WEB;
            case MOBILE -> ChannelType.MOBILE;
        };
    }

    private ChannelType mapToBankAccountChannelType(
            final MandateRelatedSupplementaryData.OriginalChannelTypeEnum payloadChannelType) {
        return switch (payloadChannelType) {
            case BATCH -> ChannelType.BATCH;
            case WEB -> ChannelType.WEB;
            case MOBILE -> ChannelType.MOBILE;
        };
    }

    private PaymentType mapToBankAccountPaymentType(
            final LocalInstrument2Choice.ProprietaryEnum proprietaryEnum
    ) {
        return switch (proprietaryEnum) {
            case REGULAR_PAYMENT -> PaymentType.REGULAR_PAYMENT;
            case ACCOUNT_ALIAS_PAYMENT -> PaymentType.ACCOUNT_ALIAS_PAYMENT;
            case REALTIME_ACCOUNT_ALIAS_PAYMENT -> PaymentType.REALTIME_ACCOUNT_ALIAS_PAYMENT;
            case ACCOUNT_DEPOSIT_PAYMENT -> PaymentType.ACCOUNT_DEPOSIT_PAYMENT;
            case REALTIME_ACCOUNT_DEPOSIT_PAYMENT -> PaymentType.REALTIME_ACCOUNT_DEPOSIT_PAYMENT;
            case FULFILL_REQUEST_FOR_PAYMENT -> PaymentType.FULFILL_REQUEST_FOR_PAYMENT;
        };
    }
}
