package ca.bnc.payment.service;

import ca.bnc.payment.adapter.limitsvelocity.LimitsVelocityAdapter;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class LimitsValidationService {

    private final LimitsVelocityAdapter limitsVelocityAdapter;

    public void validateLimits(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        limitsVelocityAdapter.simulateVelocities(paymentSimulationRequestContext);
    }
}
