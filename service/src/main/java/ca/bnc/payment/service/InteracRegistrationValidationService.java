package ca.bnc.payment.service;

import ca.bnc.payment.adapter.paymentregistration.PaymentRegistrationAdapter;
import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_payment_registration_resources.generated.model.ClientRegistration;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_payment_registration_resources.generated.model.RailRegistrationItem;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_payment_registration_resources.generated.model.Registrations;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static ca.bnc.payment.utils.ErrorConstants.CLIENT_NOT_REGISTERED_CODE;
import static ca.bnc.payment.utils.ErrorConstants.CLIENT_NOT_REGISTERED_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.PAYMENT_REGISTRATION_SERVICE_ORIGIN;
import static java.util.Objects.isNull;
import static org.springframework.util.CollectionUtils.isEmpty;

@Service
@RequiredArgsConstructor
public class InteracRegistrationValidationService {

    private final PaymentRegistrationAdapter paymentRegistrationAdapter;

    public void validateInteracRegistration(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        final Registrations registrations = paymentRegistrationAdapter.getRegistrations(paymentSimulationRequestContext).getBody();
        validateRegistrations(registrations);
    }

    private void validateRegistrations(final Registrations registrations) {
        if (isNull(registrations)
                || isEmpty(registrations.getRegistrations())
                || registrations.getRegistrations().stream()
                .map(ClientRegistration::getRailRegistration)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .noneMatch(this::isPaymentTypeDomesticInterac)) {
            throw PaymentSimulationException.badRequest(CLIENT_NOT_REGISTERED_CODE,
                            CLIENT_NOT_REGISTERED_LOG,
                            PAYMENT_REGISTRATION_SERVICE_ORIGIN,
                            NA
            );
        }
    }

    private boolean isPaymentTypeDomesticInterac(final RailRegistrationItem railRegistrationItem) {
        return railRegistrationItem.getRailType() == RailRegistrationItem.RailTypeEnum.DOMESTIC_INTERAC;
    }

}
