package ca.bnc.payment.exception;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import org.springframework.http.HttpStatus;

public record ErrorInfo(Error error, HttpStatus status) {

    public static ErrorInfo badRequest(final Error error) {
        return new ErrorInfo(error, HttpStatus.BAD_REQUEST);
    }

    public static ErrorInfo notFound(final Error error) {
        return new ErrorInfo(error, HttpStatus.NOT_FOUND);
    }

    public static ErrorInfo internalServerError(final Error error) {
        return new ErrorInfo(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
