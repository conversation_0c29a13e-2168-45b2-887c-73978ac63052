package ca.bnc.payment.exception;


import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;


public final class RetryablePaymentSimulationException extends PaymentSimulationException {
    public RetryablePaymentSimulationException(final String code, final String text, final String origin, final String rule) {
        super(ErrorInfo.internalServerError(new Error().code(code).text(text).origin(origin).rule(rule)));
    }
}
