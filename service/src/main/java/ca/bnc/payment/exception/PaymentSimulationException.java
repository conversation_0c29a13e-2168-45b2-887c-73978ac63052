package ca.bnc.payment.exception;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import lombok.Getter;

import java.util.Optional;

import static ca.bnc.payment.utils.ErrorConstants.NO_REASON_PROVIDED;

@Getter
public sealed class PaymentSimulationException extends RuntimeException permits RetryablePaymentSimulationException {

    private final ErrorInfo errorInfo;

    protected PaymentSimulationException(final ErrorInfo errorInfo) {
        super(Optional.ofNullable(errorInfo.error())
                .map(Error::getText)
                .orElse(NO_REASON_PROVIDED));
        this.errorInfo = errorInfo;
    }

    public static PaymentSimulationException badRequest(final Error error) {
        return new PaymentSimulationException(ErrorInfo.badRequest(error));
    }

    public static PaymentSimulationException notFound(final Error error) {
        return new PaymentSimulationException(ErrorInfo.notFound(error));
    }

    public static PaymentSimulationException internalServerError(final Error error) {
        return new PaymentSimulationException(ErrorInfo.internalServerError(error));
    }

    public static PaymentSimulationException badRequest(final String code,
                                                        final String text,
                                                        final String origin,
                                                        final String rule) {
        return badRequest(new Error().code(code).text(text).origin(origin).rule(rule));
    }

    public static PaymentSimulationException notFound(final String code,
                                                      final String text,
                                                      final String origin,
                                                      final String rule) {
        return notFound(new Error().code(code).text(text).origin(origin).rule(rule));
    }

    public static PaymentSimulationException internalServerError(final String code,
                                                                 final String text,
                                                                 final String origin,
                                                                 final String rule) {
        return internalServerError(new Error().code(code).text(text).origin(origin).rule(rule));
    }
}
