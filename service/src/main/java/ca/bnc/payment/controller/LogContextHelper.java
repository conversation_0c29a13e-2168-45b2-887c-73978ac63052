package ca.bnc.payment.controller;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.UUID;

@Component
public class LogContextHelper {

    private static final String LOG_FIELD_END_TO_END_BUSINESS_IDENTIFICATION = "endToEndBusinessIdentification";
    private static final String LOG_FIELD_REQUEST_ID = "requestId";
    private static final String LOG_FIELD_CLIENT_ID = "clientId";

    public Map<String, Object> contextFor(
            final String endToEndBusinessIdentification,
            final UUID requestId,
            final String clientId
    ) {
        return Map.of(
                LOG_FIELD_END_TO_END_BUSINESS_IDENTIFICATION, endToEndBusinessIdentification,
                LOG_FIELD_REQUEST_ID, requestId.toString(),
                LOG_FIELD_CLIENT_ID, clientId
        );
    }
}
