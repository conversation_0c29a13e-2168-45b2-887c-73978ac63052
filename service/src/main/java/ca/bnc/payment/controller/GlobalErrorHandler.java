package ca.bnc.payment.controller;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Error;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.Errors;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import static ca.bnc.payment.utils.ErrorConstants.GENERIC_ERROR_LOG;
import static ca.bnc.payment.utils.ErrorConstants.INTERNAL_SERVER_ERROR_GENERIC_LOG;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.REQUEST_INVALID_CODE;
import static ca.bnc.payment.utils.ErrorConstants.SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;

@ControllerAdvice
public class GlobalErrorHandler {
    private final Logger logger = LoggerFactory.getLogger(GlobalErrorHandler.class.getName());
    private final LoggingFacade loggingFacade;

    public GlobalErrorHandler(final LoggingFacade loggingFacade) {
        this.loggingFacade = loggingFacade;
    }

    @ExceptionHandler(PaymentSimulationException.class)
    public ResponseEntity<Errors> handleProcessingSimulationException(final PaymentSimulationException e) {
        logError(e.getErrorInfo().error(), e);
        return buildResponseEntity(e.getErrorInfo().error(), e.getErrorInfo().status());
    }

    @ExceptionHandler(MissingRequestHeaderException.class)
    public ResponseEntity<Errors> handleMissingRequestHeaderException(final MissingRequestHeaderException e) {
        final Error error = new Error()
                .code(REQUEST_INVALID_CODE)
                .text(e.getMessage())
                .origin(SERVICE_ORIGIN)
                .rule(NA);
        logError(error, e);
        return buildResponseEntity(error, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Errors> handleException(final Exception e) {
        Error error = new Error().code(TECHNICAL_ERROR_CODE)
                .text(INTERNAL_SERVER_ERROR_GENERIC_LOG)
                .origin(SERVICE_ORIGIN)
                .rule(NA);
        logError(error, e);
        return buildResponseEntity(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private void logError(final Error error, final Exception e) {
        String errorMessage = GENERIC_ERROR_LOG.formatted(error.getCode(), error.getText(), error.getOrigin(), error.getRule());
        loggingFacade.error(logger, errorMessage, e);
    }

    private ResponseEntity<Errors> buildResponseEntity(final Error error, final HttpStatusCode status) {
        return ResponseEntity.status(status)
                .body(new Errors().addErrorsItem(error));
    }
}
