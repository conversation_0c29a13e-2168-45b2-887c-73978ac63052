package ca.bnc.payment.controller;

import ca.nbc.payment.lib.service.logging.LogContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
@RequiredArgsConstructor
public final class LogContextCleanerInterceptor implements HandlerInterceptor {
    private final LogContextHolder logContextHolder;

    @Override
    public void afterCompletion(
            final HttpServletRequest request,
            final HttpServletResponse response,
            final Object handler,
            final Exception ex) {
        logContextHolder.reset();
    }
}
