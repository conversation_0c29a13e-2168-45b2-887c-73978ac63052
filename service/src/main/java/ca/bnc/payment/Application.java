package ca.bnc.payment;

import ca.nbc.payment.pmt_logging_library.annotations.EnableFeignLogging;
import ca.nbc.payment.pmt_logging_library.annotations.EnableHttpLogging;
import ca.nbc.payment.pmt_security_library.annotations.EnableOktaClient;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.retry.annotation.EnableRetry;

@SpringBootApplication(
        scanBasePackages = {"ca.bnc.payment", "ca.nbc.payment"}
)
@EnableFeignClients(basePackages = {"ca.nbc.payment.pmt_security_library.okta", "ca.bnc.payment.client"})
@EnableOktaClient
@EnableFeignLogging
@EnableRetry
@EnableHttpLogging
public class Application {

    public static void main(final String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
