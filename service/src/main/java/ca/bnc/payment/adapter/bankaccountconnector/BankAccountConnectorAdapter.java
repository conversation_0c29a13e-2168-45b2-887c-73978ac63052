package ca.bnc.payment.adapter.bankaccountconnector;

import ca.bnc.payment.client.bankaccountconnector.BankAccountConnectorApiClient;
import ca.bnc.payment.exception.RetryablePaymentSimulationException;
import ca.bnc.payment.mapper.bankaccountconnector.DebitRequestMapper;
import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.nbc.payment.etransfer.bankaccountconnector.model.Action;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ChannelType;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DebitRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DebitResponse;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentType;
import feign.RetryableException;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.UUID;

import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.BANK_ACCOUNT_CONNECTOR_TIMEOUT;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;

@Service
@RequiredArgsConstructor
public class BankAccountConnectorAdapter {

    private static final String DEFAULT_RETRY_INDICATOR = "false";
    private final DebitRequestMapper debitRequestMapper;
    private final BankAccountConnectorApiClient bankAccountConnectorApiClient;

    @Retryable(retryFor = {RetryablePaymentSimulationException.class},
            maxAttemptsExpression = "${providers.bank-account-connector.retry.attempts}",
            backoff = @Backoff(delayExpression = "${providers.bank-account-connector.retry.backoff}"))
    public ResponseEntity<DebitResponse> initiateDebit(
            final String endToEndBusinessIdentification,
            final PaymentSimulationRequestContext paymentSimulationRequestContext,
            final ChannelType channelType,
            final PaymentType paymentType) {
            final PaymentSimulationHeader paymentSimulationHeader = paymentSimulationRequestContext.paymentSimulationHeader();
            final DebitRequest debitRequest = debitRequestMapper.toDebitRequest(
                    endToEndBusinessIdentification,
                    paymentSimulationHeader.xClientId(),
                    String.valueOf(paymentSimulationHeader.xRequestId()),
                    paymentSimulationRequestContext.paymentRequest()
            );
        try {
            return bankAccountConnectorApiClient.initiateDebit(
                    paymentSimulationHeader.xChannelId(),
                    channelType,
                    Action.SEND,
                    paymentType,
                    paymentSimulationHeader.xClientId(),
                    DEFAULT_RETRY_INDICATOR,
                    endToEndBusinessIdentification,
                    String.valueOf(paymentSimulationHeader.xRequestId()),
                    UUID.fromString(paymentSimulationHeader.bncBusinessTraceId()),
                    paymentSimulationHeader.traceparent(),
                    paymentSimulationHeader.tracestate(),
                    debitRequest
            );
        } catch (RetryableException retryableException) {
            throw new RetryablePaymentSimulationException(
                    TECHNICAL_ERROR_CODE,
                    BANK_ACCOUNT_CONNECTOR_TIMEOUT,
                    BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN,
                    NA
            );
        }
    }
}
