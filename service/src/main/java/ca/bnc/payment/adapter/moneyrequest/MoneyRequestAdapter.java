package ca.bnc.payment.adapter.moneyrequest;

import ca.bnc.payment.client.moneyrequest.MoneyRequestApiClient;
import ca.bnc.payment.exception.RetryablePaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.model.ChannelType;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_money_request_resources.generated.model.DomesticFulfillmentMoneyRequest;
import feign.RetryableException;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.UUID;

import static ca.bnc.payment.utils.ErrorConstants.MONEY_REQUEST_API_UNAVAILABLE_LOG;
import static ca.bnc.payment.utils.ErrorConstants.MONEY_REQUEST_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;

@Service
@RequiredArgsConstructor
public class MoneyRequestAdapter {

    private static final String ACCEPT_HEADER = "application/vnd.ca.bnc.pmt+json;version=v1";

    private final MoneyRequestApiClient moneyRequestApiClient;

    @Retryable(retryFor = {RetryablePaymentSimulationException.class},
            maxAttemptsExpression = "${providers.money-request.retry.attempts}",
            backoff = @Backoff(delayExpression = "${providers.money-request.retry.backoff}"))
    public ResponseEntity<DomesticFulfillmentMoneyRequest> getIncomingMoneyRequest(
            final PaymentSimulationRequestContext paymentSimulationRequestContext
    ) {
        final String interacMoneyRequestId = paymentSimulationRequestContext
                .paymentRequest()
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getSupplementaryData()
                .getInteracMoneyRequestId();
        final PaymentSimulationHeader paymentSimulationHeader = paymentSimulationRequestContext.paymentSimulationHeader();
        try {
            return moneyRequestApiClient.getIncomingMoneyRequest(
                    interacMoneyRequestId,
                    paymentSimulationHeader.xChannelId(),
                    ChannelType.fromValue(paymentSimulationHeader.xChannelType().getValue()),
                    paymentSimulationHeader.xRequestId(),
                    paymentSimulationHeader.xClientId(),
                    ACCEPT_HEADER,
                    paymentSimulationHeader.traceparent(),
                    paymentSimulationHeader.tracestate(),
                    UUID.fromString(paymentSimulationHeader.bncBusinessTraceId()),
                    null,
                    null
            );
        } catch (RetryableException retryableException) {
            throw new RetryablePaymentSimulationException(
                    TECHNICAL_ERROR_CODE,
                    MONEY_REQUEST_API_UNAVAILABLE_LOG,
                    MONEY_REQUEST_SERVICE_ORIGIN,
                    NA
            );
        }
    }
}
