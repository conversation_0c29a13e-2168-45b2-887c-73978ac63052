package ca.bnc.payment.adapter.limitsvelocity;

import ca.bnc.payment.client.limitvelocity.LimitsVelocityApiClient;
import ca.bnc.payment.exception.RetryablePaymentSimulationException;
import ca.bnc.payment.mapper.limitsvelocity.LimitsVelocityRequestMapper;
import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.model.VelocitiesSimulation;
import feign.RetryableException;
import lombok.RequiredArgsConstructor;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import static ca.bnc.payment.utils.ErrorConstants.LIMITS_VELOCITY_API_UNAVAILABLE_LOG;
import static ca.bnc.payment.utils.ErrorConstants.LIMITS_VELOCITY_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;

@Service
@RequiredArgsConstructor
public class LimitsVelocityAdapter {

    private static final String ACCEPT_VERSION_HEADER = "v1";

    private final LimitsVelocityApiClient limitsVelocityApiClient;
    private final LimitsVelocityRequestMapper limitsVelocityRequestMapper;

    @Retryable(retryFor = {RetryablePaymentSimulationException.class},
            maxAttemptsExpression = "${providers.limits-velocity.retry.attempts}",
            backoff = @Backoff(delayExpression = "${providers.limits-velocity.retry.backoff}"))
    public void simulateVelocities(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        VelocitiesSimulation velocitiesSimulation = limitsVelocityRequestMapper.mapVelocitiesSimulation(paymentSimulationRequestContext);
        final PaymentSimulationHeader paymentSimulationHeader = paymentSimulationRequestContext.paymentSimulationHeader();
        try {
            limitsVelocityApiClient.simulationVelocities(
                    paymentSimulationHeader.xChannelId(),
                    ACCEPT_VERSION_HEADER,
                    paymentSimulationHeader.xRequestId().toString(),
                    velocitiesSimulation);
        } catch (RetryableException e) {
            throw new RetryablePaymentSimulationException(TECHNICAL_ERROR_CODE,
                    LIMITS_VELOCITY_API_UNAVAILABLE_LOG,
                    LIMITS_VELOCITY_SERVICE_ORIGIN,
                    NA
            );
        }
    }

}
