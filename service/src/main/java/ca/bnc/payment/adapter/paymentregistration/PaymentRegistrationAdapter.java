package ca.bnc.payment.adapter.paymentregistration;

import ca.bnc.payment.client.paymentregistration.PaymentRegistrationApiClient;
import ca.bnc.payment.exception.RetryablePaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_payment_registration_resources.generated.model.ChannelType;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_payment_registration_resources.generated.model.Registrations;
import feign.RetryableException;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import static ca.bnc.payment.utils.ErrorConstants.NA;
import static ca.bnc.payment.utils.ErrorConstants.PAYMENT_REGISTRATION_API_UNAVAILABLE_LOG;
import static ca.bnc.payment.utils.ErrorConstants.PAYMENT_REGISTRATION_SERVICE_ORIGIN;
import static ca.bnc.payment.utils.ErrorConstants.TECHNICAL_ERROR_CODE;

@Service
@RequiredArgsConstructor
public class PaymentRegistrationAdapter {

    private static final String ACCEPT_HEADER = "application/vnd.ca.bnc.pmt+json;version=v1";

    private final PaymentRegistrationApiClient paymentRegistrationApiClient;

    @Retryable(retryFor = {RetryablePaymentSimulationException.class},
            maxAttemptsExpression = "${providers.payment-registration.retry.attempts}",
            backoff = @Backoff(delayExpression = "${providers.payment-registration.retry.backoff}"))
    public ResponseEntity<Registrations> getRegistrations(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        final PaymentSimulationHeader paymentSimulationHeader = paymentSimulationRequestContext.paymentSimulationHeader();
        try {
            return paymentRegistrationApiClient.getRegistrations(
                    paymentSimulationHeader.traceparent(),
                    paymentSimulationHeader.tracestate(),
                    paymentSimulationHeader.bncBusinessTraceId(),
                    ACCEPT_HEADER,
                    paymentSimulationHeader.xRequestId().toString(),
                    paymentSimulationHeader.xChannelId(),
                    ChannelType.fromValue(paymentSimulationHeader.xChannelType().toString()),
                    paymentSimulationRequestContext.paymentSimulationHeader().xClientId(),
                    null,
                    null,
                    null);
        } catch (RetryableException retryableException) {
            throw new RetryablePaymentSimulationException(
                            TECHNICAL_ERROR_CODE,
                            PAYMENT_REGISTRATION_API_UNAVAILABLE_LOG,
                            PAYMENT_REGISTRATION_SERVICE_ORIGIN,
                            NA
            );
        }
    }
}
