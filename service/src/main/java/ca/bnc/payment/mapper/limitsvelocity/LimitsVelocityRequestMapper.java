package ca.bnc.payment.mapper.limitsvelocity;

import ca.bnc.payment.model.PaymentSimulationRequestContext;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.model.ClientType;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.model.LimitType;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.model.SimulationType;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.model.VelocitiesSimulation;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.FIToFICustomerCreditTransferV08;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.PaymentIdentification7;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SupplementaryData;
import ca.bnc.payment.utils.IdGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Clock;
import java.time.OffsetDateTime;
import java.util.Optional;

import static ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.model.LimitType.DOMESTIC_INTERAC_ANR;
import static ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.model.LimitType.DOMESTIC_INTERAC_MONEYREQUEST;
import static ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.model.LimitType.DOMESTIC_INTERAC_REGULAR;
import static ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_limits_velocity_resources.generated.model.LimitType.DOMESTIC_INTERAC_RTANR;

@Component
@RequiredArgsConstructor
public class LimitsVelocityRequestMapper {

    private final IdGenerator idGenerator;
    private final Clock clock;

    public VelocitiesSimulation mapVelocitiesSimulation(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        SendPaymentExecuteRequest paymentRequest = paymentSimulationRequestContext.paymentRequest();
        return new VelocitiesSimulation()
                .simulationType(SimulationType.VELOCITIES)
                .limitType(getLimitType(paymentRequest))
                .endToEndIdentification(paymentSimulationRequestContext.endToEndBusinessIdentification())
                .instructionIdentification(getInstructionIdentification(paymentRequest))
                .clientId(paymentSimulationRequestContext.paymentSimulationHeader().xClientId())
                .clientType(getClientType(paymentRequest))
                .amount(getAmount(paymentRequest))
                .postingDate(getPostingDate());
    }

    private OffsetDateTime getPostingDate() {
        return OffsetDateTime.now(clock);
    }

    private BigDecimal getAmount(final SendPaymentExecuteRequest paymentRequest) {
        return paymentRequest
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getInterbankSettlementAmount()
                .getAmount();
    }

    private ClientType getClientType(final SendPaymentExecuteRequest paymentRequest) {
        SupplementaryData.ClientTypeEnum clientTypeEnum = paymentRequest
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getSupplementaryData()
                .getClientType();
        return SupplementaryData.ClientTypeEnum.INDIVIDUAL == clientTypeEnum ? ClientType.INDIVIDUAL : ClientType.ORGANISATION;
    }

    private String getInstructionIdentification(final SendPaymentExecuteRequest paymentRequest) {
        return Optional.ofNullable(paymentRequest)
                .map(SendPaymentExecuteRequest::getFiToFICustomerCreditTransfer)
                .map(FIToFICustomerCreditTransferV08::getCreditTransferTransactionInformation)
                .map(CreditTransferTransaction39::getPaymentIdentification)
                .map(PaymentIdentification7::getInstructionIdentification)
                .orElseGet(idGenerator::generateInstructionIdentification);
    }

    private LimitType getLimitType(final SendPaymentExecuteRequest sendPaymentExecuteRequest) {
        LocalInstrument2Choice.ProprietaryEnum proprietary = sendPaymentExecuteRequest
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getPaymentTypeInformation()
                .getLocalInstrument()
                .getProprietary();
        return switch (proprietary) {
            case ACCOUNT_DEPOSIT_PAYMENT -> DOMESTIC_INTERAC_ANR;
            case REALTIME_ACCOUNT_DEPOSIT_PAYMENT -> DOMESTIC_INTERAC_RTANR;
            case FULFILL_REQUEST_FOR_PAYMENT -> DOMESTIC_INTERAC_MONEYREQUEST;
            case REGULAR_PAYMENT, ACCOUNT_ALIAS_PAYMENT, REALTIME_ACCOUNT_ALIAS_PAYMENT -> DOMESTIC_INTERAC_REGULAR;
        };
    }
}
