package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditTransferTransaction39;
import ca.nbc.payment.etransfer.bankaccountconnector.model.BranchAndFinancialInstitutionIdentification6;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ChargeBearerType1Code;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DirectDebitTransactionInformation24;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class BacDirectDebitTransactionInformation24Mapper {

    private static final String NOT_PROVIDED = "NOTPROVIDED";
    private final BacPaymentIdentification7Mapper bacPaymentIdentification7Mapper;
    private final BacPaymentTypeInformation27Mapper bacPaymentTypeInformation27Mapper;
    private final BacActiveCurrencyAndAmountMapper bacActiveCurrencyAndAmountMapper;
    private final BacBranchAndFinancialInstitutionIdentification6Mapper bacBranchAndFinancialInstitutionIdentification6Mapper;
    private final BacPartyIdentification135Mapper bacPartyIdentification135Mapper;
    private final BacCashAccount38Mapper bacCashAccount38Mapper;
    private final BacRemittanceInformation16Mapper bacRemittanceInformation16Mapper;
    private final BacDirectDebitTransaction10Mapper bacDirectDebitTransaction10Mapper;

    public DirectDebitTransactionInformation24 map(final CreditTransferTransaction39 creditTransferTransaction39) {

        final BranchAndFinancialInstitutionIdentification6 agent = bacBranchAndFinancialInstitutionIdentification6Mapper.map(NOT_PROVIDED);

        return new DirectDebitTransactionInformation24()
                .paymentIdentification(bacPaymentIdentification7Mapper.map(creditTransferTransaction39.getPaymentIdentification()))
                .paymentTypeInformation(bacPaymentTypeInformation27Mapper.map(creditTransferTransaction39.getPaymentTypeInformation()))
                .interbankSettlementAmount(bacActiveCurrencyAndAmountMapper.map(creditTransferTransaction39.getInterbankSettlementAmount()))
                .interbankSettlementDate(creditTransferTransaction39.getInterbankSettlementDate())
                .chargeBearer(ChargeBearerType1Code.SLEV)
                .creditor(bacPartyIdentification135Mapper.map(creditTransferTransaction39.getCreditor()))
                .creditorAccount(bacCashAccount38Mapper.map(creditTransferTransaction39.getCreditorAccount()))
                .creditorAgent(agent)
                .debtor(bacPartyIdentification135Mapper.map(creditTransferTransaction39.getDebtor()))
                .debtorAccount(bacCashAccount38Mapper.map(creditTransferTransaction39.getDebtorAccount()))
                .debtorAgent(agent)
                .remittanceInformation(bacRemittanceInformation16Mapper.map(creditTransferTransaction39.getRemittanceInformation()))
                .directDebitTransaction(bacDirectDebitTransaction10Mapper.map(creditTransferTransaction39.getMandateRelatedInformation()));
    }
}
