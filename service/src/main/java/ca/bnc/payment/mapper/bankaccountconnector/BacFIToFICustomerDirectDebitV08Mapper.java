package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.FIToFICustomerCreditTransferV08;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DirectDebitTransactionInformation24;
import ca.nbc.payment.etransfer.bankaccountconnector.model.FIToFICustomerDirectDebitV08;
import ca.nbc.payment.etransfer.bankaccountconnector.model.HTTPHeader;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class BacFIToFICustomerDirectDebitV08Mapper {

    private static final String RETRY_INDICATOR = "false";
    private final BacGroupHeader94Mapper bacGroupHeader94Mapper;
    private final BacDirectDebitTransactionInformation24Mapper bacDirectDebitTransactionInformation24Mapper;

    public FIToFICustomerDirectDebitV08 map(
            final String endToEndBusinessIdentification,
            final String clientId,
            final String requestId,
            final SendPaymentExecuteRequest request
    ) {

        return new FIToFICustomerDirectDebitV08()
                .groupHeader(bacGroupHeader94Mapper.map(request.getFiToFICustomerCreditTransfer().getGroupHeader()))
                .directDebitTransactionInformation(List.of(
                        toDirectDebitTransactionInformation(request.getFiToFICustomerCreditTransfer())
                ))
                .htTPHeader(toHttpHeader(endToEndBusinessIdentification, clientId, requestId));
    }

    private DirectDebitTransactionInformation24 toDirectDebitTransactionInformation(
            final FIToFICustomerCreditTransferV08 fiToFICustomerCreditTransfer) {
        return bacDirectDebitTransactionInformation24Mapper.map(fiToFICustomerCreditTransfer.getCreditTransferTransactionInformation()
        );
    }

    private HTTPHeader toHttpHeader(
            final String endToEndBusinessIdentification,
            final String clientId,
            final String requestId
    ) {
        return new HTTPHeader()
                .id(endToEndBusinessIdentification)
                .xC1ClientId(clientId)
                .xRequestId(requestId)
                .xRetryIndicator(RETRY_INDICATOR);
    }
}
