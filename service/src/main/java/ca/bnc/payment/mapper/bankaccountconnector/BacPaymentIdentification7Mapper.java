package ca.bnc.payment.mapper.bankaccountconnector;

import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentIdentification7;
import org.springframework.stereotype.Component;

@Component
public class BacPaymentIdentification7Mapper {

    public PaymentIdentification7 map(
            final ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.PaymentIdentification7
                    paymentIdentification7
            ) {
        return new PaymentIdentification7()
                .endToEndIdentification(paymentIdentification7.getEndToEndIdentification())
                .instructionIdentification(paymentIdentification7.getInstructionIdentification())
                .transactionIdentification(paymentIdentification7.getEndToEndIdentification());
    }
}
