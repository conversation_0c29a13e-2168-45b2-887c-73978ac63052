package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.PaymentTypeInformation28;
import ca.nbc.payment.etransfer.bankaccountconnector.model.LocalInstrument2Choice;
import ca.nbc.payment.etransfer.bankaccountconnector.model.LocalInstrument2ChoiceProprietary;
import ca.nbc.payment.etransfer.bankaccountconnector.model.LocalInstrument2ChoiceProprietary.ProprietaryEnum;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentTypeInformation27;
import org.springframework.stereotype.Component;

import static ca.bnc.payment.mapper.bankaccountconnector.BankAccountConnectorObjectTypes.PROPRIETARY;

@Component
public class BacPaymentTypeInformation27Mapper {

    public PaymentTypeInformation27 map(final PaymentTypeInformation28 paymentTypeInformation) {

        return new PaymentTypeInformation27()
                .localInstrument(
                        localInstrument(
                                mapLocalInstrumentProprietary(paymentTypeInformation.getLocalInstrument().getProprietary())
                        )
                );
    }

    private LocalInstrument2Choice localInstrument(final ProprietaryEnum proprietaryEnum) {
        return new LocalInstrument2ChoiceProprietary()
                .objectType(PROPRIETARY)
                .proprietary(proprietaryEnum);
    }

    private ProprietaryEnum mapLocalInstrumentProprietary(
            final ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources
                    .generated.model.LocalInstrument2Choice.ProprietaryEnum source) {
        return ProprietaryEnum.fromValue(source.getValue());
    }
}
