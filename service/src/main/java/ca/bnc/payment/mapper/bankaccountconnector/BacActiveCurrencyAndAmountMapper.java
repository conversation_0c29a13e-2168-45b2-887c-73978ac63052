package ca.bnc.payment.mapper.bankaccountconnector;

import ca.nbc.payment.etransfer.bankaccountconnector.model.ActiveCurrencyAndAmount;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ActiveCurrencyCode;
import org.springframework.stereotype.Component;

@Component
public class BacActiveCurrencyAndAmountMapper {

    public ActiveCurrencyAndAmount map(
            final ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources
                    .generated.model.ActiveCurrencyAndAmount activeCurrencyAndAmount) {
        return new ActiveCurrencyAndAmount()
                .amount(activeCurrencyAndAmount.getAmount())
                .currency(mapCurrencyCode(activeCurrencyAndAmount.getCurrency()));
    }

    private ActiveCurrencyCode mapCurrencyCode(
            final ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources
                    .generated.model.ActiveCurrencyCode activeCurrencyCode) {
        return ActiveCurrencyCode.fromValue(activeCurrencyCode.toString());
    }
}
