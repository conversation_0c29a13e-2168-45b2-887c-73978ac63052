package ca.bnc.payment.mapper.bankaccountconnector;

import ca.nbc.payment.etransfer.bankaccountconnector.model.ClearingSystemIdentification3Choice;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ClearingSystemIdentification3ChoiceProprietary;
import ca.nbc.payment.etransfer.bankaccountconnector.model.SettlementInstruction8;
import ca.nbc.payment.etransfer.bankaccountconnector.model.SettlementMethod2Code;
import org.springframework.stereotype.Component;

import static ca.bnc.payment.mapper.bankaccountconnector.BankAccountConnectorObjectTypes.PROPRIETARY;

@Component
public class BacSettlementInstruction8Mapper {
    public SettlementInstruction8 map() {
        return new SettlementInstruction8()
                .settlementMethod(SettlementMethod2Code.CLRG)
                .clearingSystem(clearingSystem());
    }

    private ClearingSystemIdentification3Choice clearingSystem() {
        return new ClearingSystemIdentification3ChoiceProprietary()
                .objectType(PROPRIETARY)
                .proprietary(ClearingSystemIdentification3ChoiceProprietary.ProprietaryEnum.ETR);
    }
}
