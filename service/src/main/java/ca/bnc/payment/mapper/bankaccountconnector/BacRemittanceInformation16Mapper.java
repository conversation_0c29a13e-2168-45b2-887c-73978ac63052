package ca.bnc.payment.mapper.bankaccountconnector;

import ca.nbc.payment.etransfer.bankaccountconnector.model.RemittanceInformation16;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class BacRemittanceInformation16Mapper {

    public RemittanceInformation16 map(
            final ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources
                    .generated.model.RemittanceInformation16 remittanceInformation) {

        return Optional.ofNullable(remittanceInformation)
                .map(remittanceInformation1 ->
                        new RemittanceInformation16().unstructured(remittanceInformation1.getUnstructured()))
                .orElse(null);

    }
}
