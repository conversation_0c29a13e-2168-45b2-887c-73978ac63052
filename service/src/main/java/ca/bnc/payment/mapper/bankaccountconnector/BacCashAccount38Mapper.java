package ca.bnc.payment.mapper.bankaccountconnector;

import ca.nbc.payment.etransfer.bankaccountconnector.model.AccountIdentification4Choice;
import ca.nbc.payment.etransfer.bankaccountconnector.model.AccountIdentification4ChoiceOther;
import ca.nbc.payment.etransfer.bankaccountconnector.model.AccountSchemeName1ChoiceProprietary;
import ca.nbc.payment.etransfer.bankaccountconnector.model.CashAccount38;
import ca.nbc.payment.etransfer.bankaccountconnector.model.GenericAccountIdentification1;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static ca.bnc.payment.mapper.bankaccountconnector.BankAccountConnectorObjectTypes.OTHER;
import static ca.bnc.payment.mapper.bankaccountconnector.BankAccountConnectorObjectTypes.PROPRIETARY;

@Component
public class BacCashAccount38Mapper {

    public CashAccount38 map(final ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources
            .generated.model.CashAccount38 cashAccount) {
        return Optional.ofNullable(cashAccount)
                .map(cashAccount38 ->
                        new CashAccount38()
                                .identification(identification(cashAccount.getIdentification().getOther().getIdentification())))
                .orElse(null);
    }

    private static AccountIdentification4Choice identification(final String identification) {
        return new AccountIdentification4ChoiceOther()
                .objectType(OTHER)
                .other(new GenericAccountIdentification1()
                        .identification(identification)
                        .schemeName(new AccountSchemeName1ChoiceProprietary()
                                .objectType(PROPRIETARY)
                                .proprietary(AccountSchemeName1ChoiceProprietary.ProprietaryEnum.BANK_ACCT_NO)));
    }
}
