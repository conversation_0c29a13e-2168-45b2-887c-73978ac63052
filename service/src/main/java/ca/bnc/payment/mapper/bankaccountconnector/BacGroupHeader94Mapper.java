package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.GroupHeader93;
import ca.nbc.payment.etransfer.bankaccountconnector.model.GroupHeader94;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class BacGroupHeader94Mapper {

    private static final String NUMBER_OF_TRANSACTIONS = "1";
    private static final String DIRECT_PARTICIPANT_IDENTIFIER = "CA000006";
    private static final String INSTRUCTED_AGENT_MEMBER_IDENTIFICATION = "NOTPROVIDED";

    private final BacSettlementInstruction8Mapper bacSettlementInstruction8Mapper;
    private final BacBranchAndFinancialInstitutionIdentification6Mapper bacBranchAndFinancialInstitutionIdentification6Mapper;


    public GroupHeader94 map(final GroupHeader93 groupHeader93) {
        return new GroupHeader94()
                .messageIdentification(groupHeader93.getMessageIdentification())
                .creationDatetime(groupHeader93.getCreationDateTime())
                .numberOfTransactions(NUMBER_OF_TRANSACTIONS)
                .settlementInformation(bacSettlementInstruction8Mapper.map())
                .instructingAgent(bacBranchAndFinancialInstitutionIdentification6Mapper.map(DIRECT_PARTICIPANT_IDENTIFIER))
                .instructedAgent(bacBranchAndFinancialInstitutionIdentification6Mapper.map(INSTRUCTED_AGENT_MEMBER_IDENTIFICATION));
    }
}
