package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditorContact;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.DebtorContact;
import ca.nbc.payment.etransfer.bankaccountconnector.model.Contact4;
import org.springframework.stereotype.Component;

@Component
public class BacContact4Mapper {

    public Contact4 map(final CreditorContact creditorContact) {
        return new Contact4()
                .mobileNumber(creditorContact.getMobileNumber())
                .emailAddress(creditorContact.getEmailAddress());
    }

    public Contact4 map(final DebtorContact debtorContact) {
        return new Contact4()
                .mobileNumber(debtorContact.getMobileNumber())
                .emailAddress(debtorContact.getEmailAddress());
    }

}
