package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditorIdentification;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.DebtorIdentification;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PartyIdentification135;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class BacPartyIdentification135Mapper {

    private final BacContact4Mapper bacContact4Mapper;

    public PartyIdentification135 map(final CreditorIdentification creditorIdentification) {
        return new PartyIdentification135()
                .name(creditorIdentification.getName())
                .contactDetails(bacContact4Mapper.map(creditorIdentification.getContactDetails()));
    }

    public PartyIdentification135 map(final DebtorIdentification debtorIdentification) {
        return new PartyIdentification135()
                .name(debtorIdentification.getName())
                .contactDetails(bacContact4Mapper.map(debtorIdentification.getContactDetails()));
    }
}
