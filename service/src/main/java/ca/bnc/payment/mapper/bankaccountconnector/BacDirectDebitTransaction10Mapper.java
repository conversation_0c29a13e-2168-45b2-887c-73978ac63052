package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.MandateRelatedInformation;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DirectDebitTransaction10;
import ca.nbc.payment.etransfer.bankaccountconnector.model.MandateRelatedInformation14;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class BacDirectDebitTransaction10Mapper {

    public DirectDebitTransaction10 map(final MandateRelatedInformation mandateRelatedInformation) {

        return Optional.ofNullable(mandateRelatedInformation)
                .map(mandateRelatedInformation1 ->
                        new DirectDebitTransaction10()
                                .mandateRelatedInformation(
                                        mandateRelatedInformation(mandateRelatedInformation1.getMandateIdentification())))
                .orElse(null);
    }

    private static MandateRelatedInformation14 mandateRelatedInformation(final String mandateIdentification) {
        return new MandateRelatedInformation14().mandateIdentification(mandateIdentification);
    }
}
