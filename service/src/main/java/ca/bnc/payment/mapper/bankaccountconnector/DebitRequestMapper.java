package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DebitRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DebitRequestMapper {

    private final BacFIToFICustomerDirectDebitV08Mapper bacFIToFICustomerDirectDebitV08Mapper;

    public DebitRequest toDebitRequest(
            final String endToEndBusinessIdentification,
            final String clientId,
            final String requestId,
            final SendPaymentExecuteRequest request
    ) {
        return new DebitRequest().fiToFiCustomerDebitTransfer(
                bacFIToFICustomerDirectDebitV08Mapper.map(endToEndBusinessIdentification, clientId, requestId, request)
        );
    }
}
