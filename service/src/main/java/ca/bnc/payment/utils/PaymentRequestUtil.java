package ca.bnc.payment.utils;

import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditorContact;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditorIdentification;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.FIToFICustomerCreditTransferV08;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.MandateRelatedSupplementaryData;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class PaymentRequestUtil {

    public LocalInstrument2Choice.ProprietaryEnum getProprietary(final SendPaymentExecuteRequest sendPaymentExecuteRequest) {
        return sendPaymentExecuteRequest
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getPaymentTypeInformation()
                .getLocalInstrument()
                .getProprietary();
    }

    public MandateRelatedSupplementaryData.OriginalChannelTypeEnum getOriginalChannelType(
            final SendPaymentExecuteRequest sendPaymentExecuteRequest) {
        return sendPaymentExecuteRequest
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getMandateRelatedInformation()
                .getSupplementaryData()
                .getOriginalChannelType();
    }

    public String getEmailAddress(final SendPaymentExecuteRequest paymentRequest) {
        return Optional.ofNullable(paymentRequest)
                .map(SendPaymentExecuteRequest::getFiToFICustomerCreditTransfer)
                .map(FIToFICustomerCreditTransferV08::getCreditTransferTransactionInformation)
                .map(CreditTransferTransaction39::getCreditor)
                .map(CreditorIdentification::getContactDetails)
                .map(CreditorContact::getEmailAddress)
                .orElse(null);
    }

    public String getMobileNumber(final SendPaymentExecuteRequest sendPaymentExecuteRequest) {
        return Optional.ofNullable(sendPaymentExecuteRequest)
                .map(SendPaymentExecuteRequest::getFiToFICustomerCreditTransfer)
                .map(FIToFICustomerCreditTransferV08::getCreditTransferTransactionInformation)
                .map(CreditTransferTransaction39::getCreditor)
                .map(CreditorIdentification::getContactDetails)
                .map(CreditorContact::getMobileNumber)
                .orElse(null);
    }
}
