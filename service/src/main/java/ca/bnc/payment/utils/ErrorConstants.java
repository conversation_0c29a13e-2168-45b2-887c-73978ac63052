package ca.bnc.payment.utils;

public final class ErrorConstants {

    public static final String REQUEST_INVALID_CODE = "REQUEST_INVALID";
    public static final String TECHNICAL_ERROR_CODE = "TECHNICAL_ERROR";
    public static final String PAYMENT_AUTH_REQUEST_INVALID_CODE = "PAYMENT_AUTH_REQUEST_INVALID";
    public static final String MANDATE_RELATED_INFORMATION_MISSING_CODE = "MANDATE_RELATED_INFORMATION_MISSING";
    public static final String CLIENT_NOT_REGISTERED_CODE = "CLIENT_NOT_REGISTERED";
    public static final String CANNOT_DUE_REQUEST_STATUS_CODE = "CANNOT_DUE_REQUEST_STATUS";
    public static final String INTERAC_MONEY_REQUEST_ID_MISSING_CODE = "INTERAC_MONEY_REQUEST_ID_MISSING";
    public static final String CREDITOR_NAME_MISSING_CODE = "CREDITOR_NAME_MISSING";
    public static final String INVALID_CREDITOR_LANGUAGE_CODE = "INVALID_CREDITOR_LANGUAGE";
    public static final String CONTACT_DETAILS_INVALID_CODE = "CONTACT_DETAILS_INVALID";
    public static final String CONTACT_DETAILS_MISSING_CODE = "CONTACT_DETAILS_MISSING";
    public static final String INVALID_REMITTANCE_REGEX_CODE = "INVALID_REMITTANCE_REGEX";
    public static final String CREDITOR_ID_MISSING_CODE = "CREDITOR_ID_MISSING";
    public static final String AUTODEPOSIT_REG_NUMBER_MISSING_CODE = "AUTODEPOSIT_REG_NUMBER_MISSING";
    public static final String CUSTOMER_NOT_EXIST_CODE = "CUSTOMER_NOT_EXIST";

    public static final String SERVICE_ORIGIN = "pmt-etransfer-processing-simulation-api";
    public static final String PAYMENT_REGISTRATION_SERVICE_ORIGIN = "pmt-registration-api";
    public static final String MONEY_REQUEST_SERVICE_ORIGIN = "pmt-incoming-money-request-api";
    public static final String LIMITS_VELOCITY_SERVICE_ORIGIN = "pmt-limits-velocities-api";

    public static final String NA = "NA";

    public static final String GENERIC_ERROR_LOG = "code: %s | text: %s | origin: %s | rule: %s";
    public static final String INTERNAL_SERVER_ERROR_GENERIC_LOG = "An unexpected error occurred.";
    public static final String PAYMENT_AUTH_REQUEST_INVALID_LOG = "Authentication information is missing or not complete";
    public static final String MANDATE_RELATED_INFORMATION_MISSING_LOG = "Mandate related information is missing";
    public static final String CREDITOR_NAME_IS_MISSING_LOG = "Creditor name is missing";
    public static final String CANNOT_DUE_REQUEST_STATUS_LOG = "Creditor (customer) is not allowed to perform this operation due to "
            + "current status of the request for payment";
    public static final String INTERAC_MONEY_REQUEST_ID_MISSING_LOG = "Interac money request ID is missing";
    public static final String INVALID_CREDITOR_LANGUAGE_LOG = "Invalid creditor preferred language";
    public static final String CONTACT_DETAILS_INVALID_LOG = "Only one handle is allowed: Mobile number or email address";
    public static final String CONTACT_DETAILS_MISSING_LOG = "At least one handle must be provided: Mobile number or email address";
    public static final String INVALID_REMITTANCE_REGEX_LOG = "Remittance information doesn't match the validation regex";
    public static final String CREDITOR_ID_MISSING_LOG =
            "supplementaryData.creditorId element is mandatory for a regular payment.";
    public static final String AUTODEPOSIT_REG_NUMBER_MISSING_LOG =
            "supplementaryData.creditorAutoDepositRegNumber field is mandatory for an auto-deposit payment";
    public static final String PAYMENT_REGISTRATION_API_UNAVAILABLE_LOG = "The Registration API is unreachable";
    public static final String MONEY_REQUEST_API_UNAVAILABLE_LOG = "The incoming Money Request API is unreachable";
    public static final String LIMITS_VELOCITY_API_UNAVAILABLE_LOG = "The Limits Velocities API is unreachable";
    public static final String CLIENT_NOT_REGISTERED_LOG = "Client Not Registered to Interac";
    public static final String UNEXPECTED_ERROR_LOG = "An unexpected error occurred.";
    public static final String CUSTOMER_NOT_EXIST_LOG = "Customer does not exist, or is not registered for this service";
    public static final String TOKEN_GENERATION_FAILED_LOG = "The token generation failed";
    public static final String REGISTRATION_API_UNAVAILABLE_LOG = "The Registration API is unavailable";
    public static final String NO_REASON_PROVIDED = "No reason provided";

    public static final String BANK_ACCOUNT_CONNECTOR_SERVICE_ORIGIN = "pmt-bank-account-connector-api";
    public static final String BANK_ACCOUNT_CONNECTOR_BAD_REQUEST = "Bad INITIATE ACCOUNT DEBIT request received by "
            + "the Bank Account Connector API";
    public static final String BANK_ACCOUNT_CONNECTOR_TIMEOUT = "The Bank Account Connector API is unreachable";
    public static final String BANK_ACCOUNT_CONNECTOR_SERVICE_UNAVAILABLE =
            "The Bank Account Connector API is not ready to handle the INITIATE ACCOUNT DEBIT request.";
    public static final String BANK_ACCOUNT_CONNECTOR_INTERNAL_SERVER_ERROR = "The Bank Account Connector API encountered "
            + "an unexpected condition that prevented it from fulfilling the INITIATE ACCOUNT DEBIT request";

    public static final String BANK_ACCOUNT_CONNECTOR_ERR_TABLE = "BacErrCodeToNormalizedErrCode";

    private ErrorConstants() {
    }
}
