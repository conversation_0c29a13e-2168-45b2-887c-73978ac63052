package ca.bnc.payment.utils;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Getter
public class ParticipantIdUtil {

    private static final String BNC_DIRECT_PARTICIPANT_ID = "CA000006";
    private static final String OS_DIRECT_PARTICIPANT_ID = "CA000612";

    private final String directParticipantIdentifier;

    public ParticipantIdUtil(@Value("${app.directParticipantIdentifier}") final String directParticipantIdentifier) {
        this.directParticipantIdentifier = directParticipantIdentifier;
    }

    public boolean isBncParticipant() {
        return BNC_DIRECT_PARTICIPANT_ID.equalsIgnoreCase(directParticipantIdentifier);
    }

    public boolean isOsParticipant() {
        return OS_DIRECT_PARTICIPANT_ID.equalsIgnoreCase(directParticipantIdentifier);
    }

}
